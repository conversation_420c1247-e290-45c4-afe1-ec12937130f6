# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
from tkinter import messagebox
import json
import os

# 导入主程序的类
import sys
import importlib.util

def load_main_module():
    """动态加载主程序模块"""
    spec = importlib.util.spec_from_file_location("main_module", "A大纲生成 - 副本.py")
    main_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(main_module)
    return main_module

try:
    main_module = load_main_module()
    PlottingSimulatorApp = main_module.PlottingSimulatorApp
    MainApplication = main_module.MainApplication
except Exception as e:
    print(f"无法导入主程序模块: {e}")
    exit()

def test_main_program_combo():
    """测试主程序中的组合框是否正确显示所有选项"""
    
    # 加载规则数据
    rules_data = {}
    try:
        with open("rules.json", 'r', encoding='utf-8') as f:
            rules_data = json.load(f).get("result_matrix", {})
    except Exception as e:
        messagebox.showerror("错误", f"无法加载 rules.json: {e}")
        return
    
    # 创建主窗口
    root = tk.Tk()
    root.title("主程序组合框验证")
    root.geometry("1200x800")
    
    # 创建主程序实例
    status_var = tk.StringVar(value="正在验证组合框...")
    app = PlottingSimulatorApp(root, status_var, rules_data)
    app.pack(expand=True, fill='both', padx=5, pady=5)
    
    # 状态栏
    status_bar = ttk.Label(root, textvariable=status_var, relief=tk.SUNKEN, anchor=tk.W)
    status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    # 验证函数
    def verify_combo_after_load():
        """在程序加载完成后验证组合框"""
        try:
            combo = app.combination_combo
            values = combo['values']
            
            print(f"\n=== 主程序组合框验证结果 ===")
            print(f"组合框选项数量: {len(values)}")
            print(f"预期数量: 49")
            print(f"验证结果: {'✓ 成功' if len(values) == 49 else '✗ 失败'}")
            
            if len(values) == 49:
                status_var.set("✓ 验证成功！组合框包含所有49个选项")
                messagebox.showinfo("验证成功", "组合框正确显示了所有49个选项！\n\n请点击组合框下拉箭头查看所有选项。")
            else:
                status_var.set(f"✗ 验证失败！组合框只有{len(values)}个选项")
                messagebox.showerror("验证失败", f"组合框只包含{len(values)}个选项，应该有49个！")
            
            # 显示前几个选项作为示例
            print(f"\n前10个选项:")
            for i, option in enumerate(values[:10]):
                print(f"  {i+1:2d}: {option}")
            
            if len(values) > 10:
                print(f"  ... 还有 {len(values) - 10} 个选项")
                
        except Exception as e:
            print(f"验证过程中出错: {e}")
            status_var.set(f"验证出错: {e}")
    
    # 延迟执行验证，确保界面完全加载
    root.after(1000, verify_combo_after_load)
    
    print("主程序已启动，正在验证组合框...")
    print("请查看弹出的消息框和控制台输出")
    
    root.mainloop()

if __name__ == "__main__":
    test_main_program_combo()
