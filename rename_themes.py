# -*- coding: utf-8 -*-

import os
import json
import shutil

def rename_themes():
    """重命名主题文件和内容"""
    
    print("🔧 开始重命名主题文件")
    print("=" * 50)
    
    # 定义重命名映射
    rename_mapping = {
        # 生命力量
        "生命受威胁": "生命",
        "安全有隐患": "安全", 
        "地位不稳定": "地位",
        "发展受阻碍": "发展",
        "错失良机": "机会",
        "健康出问题": "健康",
        "底线遭突破": "底线",
        "退路被切断": "退路",
        "潜力被压制": "潜力",
        "任期将结束": "任期",
        
        # 精神力量
        "权威受挑战": "权威",
        "权力被削弱": "权力",
        "影响力下降": "影响力",
        "威望受损": "威望",
        "声望被诋毁": "声望",
        "遭遇信任危机": "信任",
        "荣誉被玷污": "荣誉",
        "丧失主动权": "主动权",
        "人格魅力失效": "人格魅力",
        "信念动摇": "信念"
    }
    
    # 处理每个能量文件夹
    energy_folders = ["生命力量", "精神力量", "思维力量", "物质资源", "信息资源", "关系资源"]
    
    for energy_folder in energy_folders:
        folder_path = f"组合能量/{energy_folder}"
        if not os.path.exists(folder_path):
            print(f"❌ 文件夹不存在: {folder_path}")
            continue
            
        print(f"\n📁 处理 {energy_folder}")
        
        # 获取所有json文件
        files = [f for f in os.listdir(folder_path) if f.endswith('.json')]
        
        for file in files:
            old_theme_name = file.replace('.json', '')
            
            # 检查是否需要重命名
            if old_theme_name in rename_mapping:
                new_theme_name = rename_mapping[old_theme_name]
                old_file_path = os.path.join(folder_path, file)
                new_file_path = os.path.join(folder_path, f"{new_theme_name}.json")
                
                print(f"  📝 {old_theme_name} → {new_theme_name}")
                
                try:
                    # 读取文件内容
                    with open(old_file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 修改内容中的theme_name
                    for situation_type in ["机遇", "危机"]:
                        if situation_type in data:
                            for theme_obj in data[situation_type]:
                                theme_obj["theme_name"] = new_theme_name
                                
                                # 修改境况名称
                                for category in ["自主", "互动", "强制", "运气"]:
                                    if category in theme_obj:
                                        if situation_type == "机遇":
                                            theme_obj[category]["name"] = f"{new_theme_name}机遇"
                                        else:
                                            theme_obj[category]["name"] = f"{new_theme_name}危机"
                    
                    # 保存到新文件
                    with open(new_file_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=4)
                    
                    # 删除旧文件
                    os.remove(old_file_path)
                    
                    print(f"    ✅ 文件重命名成功")
                    
                except Exception as e:
                    print(f"    ❌ 处理失败: {e}")
            else:
                print(f"  ⏭️ 跳过: {old_theme_name} (无需重命名)")
    
    print("\n" + "=" * 50)
    print("✅ 主题重命名完成")

def update_program_tactics():
    """更新程序中的theme_tactics字典"""
    
    print("\n🔧 生成新的theme_tactics代码")
    print("=" * 50)
    
    # 新的主题对策反制
    new_theme_tactics = {
        # 生命力量
        "生命": {
            "对策": ["金蝉脱壳", "躲入暗处", "伪造身份", "寻求公义", "假死脱身", "投靠强敌", "鱼死网破", "绑架人质"],
            "反制": ["买凶杀人", "天罗地网", "识破伪装", "官官相护", "识破诡计", "借刀杀人", "早有准备", "拒不受胁"]
        },
        "安全": {
            "对策": ["深居简出", "暗中调查", "设置陷阱", "寻求庇护", "利益交换", "雇佣保镖", "先发制人", "制造伪证"],
            "反制": ["暗中布局", "加紧监视", "隐藏更深", "釜底抽薪", "出尔反尔", "收买保镖", "早有准备", "找出破绽"]
        },
        "地位": {
            "对策": ["谨言慎行", "整顿下属", "展现能力", "拜访前辈", "向上靠拢", "平衡派系", "安插亲信", "排除异己"],
            "反制": ["煽动下属", "抓住把柄", "火上浇油", "从中作梗", "恶意中伤", "两面开火", "策反亲信", "拼死反扑"]
        },
        "发展": {
            "对策": ["做出政绩", "韬光养晦", "学习深造", "投靠派系", "利益捆绑", "请求调动", "越级上报", "强行推动"],
            "反制": ["处处掣肘", "窃取功劳", "制造障碍", "从中作梗", "破坏合作", "从中作梗", "倒打一耙", "联合抵制"]
        },
        "机会": {
            "对策": ["自我反省", "创造机会", "等待下次", "弥补关系", "寻求补偿", "交换资源", "破坏他人", "强行索取"],
            "反制": ["捷足先登", "恶意嘲讽", "从中破坏", "不屑一顾", "断然拒绝", "毫无诚意", "早有防备", "强硬抵抗"]
        },
        "健康": {
            "对策": ["遍寻名医", "修身养性", "隐瞒病情", "请求御医", "交换秘方", "求取丹药", "夺取药材", "嫁祸于人"],
            "反制": ["暗中下毒", "收买名医", "察觉异常", "从中作梗", "提供假药", "恶意羞辱", "设下埋伏", "反向调查"]
        },
        "底线": {
            "对策": ["坚守原则", "辞官退隐", "内心挣扎", "寻求支持", "仗义执言", "寻找同道", "奋起反抗", "实名举报"],
            "反制": ["威逼利诱", "残酷打压", "步步为营", "威胁支持者", "杀鸡儆猴", "安插内鬼", "强力镇压", "压下举报"]
        },
        "退路": {
            "对策": ["置之死地", "绝地反击", "另辟蹊径", "请求援助", "舍命相救", "跪求原谅", "殊死一搏", "挟持人质"],
            "反制": ["赶尽杀绝", "早有预料", "拼死抵抗", "围点打援", "一网打尽", "百般羞辱", "实力碾压", "拒不受胁"]
        },
        "潜力": {
            "对策": ["厚积薄发", "磨练心性", "自暴自弃", "良禽择木", "毛遂自荐", "得遇伯乐", "锋芒毕露", "取而代之"],
            "反制": ["打压新人", "窃取成果", "恶意解读", "从中作梗", "公开打压", "恶意中伤", "强力打压", "拼死反扑"]
        },
        "任期": {
            "对策": ["安享晚年", "最后布局", "撰写回忆", "推荐继任", "政治交易", "被挽留", "贪污敛财", "清除政敌"],
            "反制": ["提前布局", "翻出旧案", "将计就计", "从中作梗", "毫无诚意", "虚情假意", "掌握证据", "拼死反扑"]
        },
        
        # 精神力量
        "权威": {
            "对策": ["做出表率", "坚定立场", "恪守原则", "上级认可", "收买人心", "获得支持", "杀鸡儆猴", "公开立威"],
            "反制": ["公开质疑", "指责作秀", "孤立主角", "越级告状", "将计就计", "分化瓦解", "煽动众人", "当众打脸"]
        },
        "权力": {
            "对策": ["韬光养晦", "分析局势", "蛰伏待机", "利益交换", "请求支援", "寻求联合", "集权一身", "清除异己"],
            "反制": ["釜底抽薪", "步步紧逼", "斩草除根", "毫无诚意", "从中作梗", "分化瓦解", "阳奉阴违", "拼死反扑"]
        },
        "影响力": {
            "对策": ["重塑形象", "扩大声势", "展示实力", "寻求盟友", "媒体造势", "公关活动", "强势回归", "重新定位"],
            "反制": ["恶意中伤", "封锁消息", "暗中破坏", "孤立主角", "负面宣传", "抵制活动", "强力压制", "边缘化"]
        },
        "威望": {
            "对策": ["澄清事实", "重建信任", "以退为进", "寻求证人", "公开道歉", "承担责任", "反击造谣", "重新证明"],
            "反制": ["恶意传播", "混淆视听", "拒不配合", "收买证人", "拒绝道歉", "推卸责任", "继续造谣", "质疑能力"]
        },
        "声望": {
            "对策": ["正面回应", "寻找证据", "反击造谣", "法律维权", "媒体澄清", "证人作证", "以德报怨", "重新证明"],
            "反制": ["散布谣言", "伪造证据", "继续造谣", "法律阻挠", "媒体封锁", "收买证人", "恶意报复", "质疑动机"]
        },
        "信任": {
            "对策": ["坦诚相待", "重建关系", "证明清白", "寻求理解", "承认错误", "弥补过失", "重新开始", "以行证言"],
            "反制": ["怀疑一切", "拒绝沟通", "质疑动机", "拒不理解", "拒绝原谅", "追究到底", "断绝关系", "以言代行"]
        },
        "荣誉": {
            "对策": ["维护名誉", "寻求正义", "以德报怨", "重新证明", "法律维权", "公开澄清", "坚持真相", "时间证明"],
            "反制": ["恶意玷污", "阻挠正义", "以怨报德", "质疑能力", "法律阻挠", "封锁消息", "歪曲真相", "持续打击"]
        },
        "主动权": {
            "对策": ["重新布局", "寻找机会", "主动出击", "创造条件", "改变策略", "联合盟友", "突破困局", "重夺主动"],
            "反制": ["严密控制", "封锁机会", "被动防守", "破坏条件", "维持现状", "分化盟友", "加强困局", "保持主动"]
        },
        "人格魅力": {
            "对策": ["重塑形象", "改变策略", "寻找新路", "学习提升", "真诚待人", "展示才华", "重建魅力", "以德服人"],
            "反制": ["恶意中伤", "阻挠改变", "封锁道路", "阻止提升", "虚假待人", "掩盖才华", "破坏魅力", "以力压人"]
        },
        "信念": {
            "对策": ["坚定信念", "寻找支撑", "重新定位", "学习充实", "寻求指导", "内心修炼", "重建信心", "坚持理想"],
            "反制": ["动摇信念", "破坏支撑", "混乱定位", "阻止学习", "误导方向", "干扰修炼", "打击信心", "破坏理想"]
        }
    }
    
    # 生成代码
    code = "        # 主题专用对策和反制字典\n        self.theme_tactics = {\n"
    for theme_name, tactics in new_theme_tactics.items():
        code += f'            "{theme_name}": {{\n'
        code += f'                "对策": {tactics["对策"]},\n'
        code += f'                "反制": {tactics["反制"]}\n'
        code += f'            }},\n'
    code = code.rstrip(',\n') + '\n        }'
    
    # 保存代码
    with open("new_theme_tactics_code.txt", "w", encoding="utf-8") as f:
        f.write(code)
    
    print("💾 新的theme_tactics代码已保存到 new_theme_tactics_code.txt")
    print(f"📊 包含 {len(new_theme_tactics)} 个主题的对策反制")

if __name__ == "__main__":
    rename_themes()
    update_program_tactics()
