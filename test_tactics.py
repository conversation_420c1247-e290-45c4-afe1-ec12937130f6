# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

def test_tactics():
    """测试对策和反制显示"""
    
    root = tk.Tk()
    root.title("对策反制测试")
    root.geometry("1000x700")
    
    print("🔍 测试对策和反制")
    print("=" * 50)
    
    # 主题专用对策和反制字典
    theme_tactics = {
        "生命受威胁": {
            "对策": ["金蝉脱壳", "躲入暗处", "伪造身份", "寻求公义", "假死脱身", "投靠强敌", "鱼死网破", "绑架人质"],
            "反制": ["买凶杀人", "天罗地网", "识破伪装", "官官相护", "识破诡计", "借刀杀人", "早有准备", "拒不受胁"]
        },
        "安全有隐患": {
            "对策": ["深居简出", "暗中调查", "设置陷阱", "寻求庇护", "利益交换", "雇佣保镖", "先发制人", "制造伪证"],
            "反制": ["暗中布局", "加紧监视", "隐藏更深", "釜底抽薪", "出尔反尔", "收买保镖", "早有准备", "找出破绽"]
        },
        "地位不稳定": {
            "对策": ["谨言慎行", "整顿下属", "展现能力", "拜访前辈", "向上靠拢", "平衡派系", "安插亲信", "排除异己"],
            "反制": ["煽动下属", "抓住把柄", "火上浇油", "从中作梗", "恶意中伤", "两面开火", "策反亲信", "拼死反扑"]
        },
        "权威受挑战": {
            "对策": ["做出表率", "坚定立场", "恪守原则", "上级认可", "收买人心", "获得支持", "杀鸡儆猴", "公开立威"],
            "反制": ["公开质疑", "指责作秀", "孤立主角", "越级告状", "将计就计", "分化瓦解", "煽动众人", "当众打脸"]
        }
    }
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🎯 对策反制测试", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=10)
    
    # 创建滚动框架
    canvas = tk.Canvas(main_frame, bg="white")
    scrollbar = tk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg="white")
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # 显示每个主题的对策和反制
    for theme_name, tactics in theme_tactics.items():
        # 主题标题
        theme_frame = tk.LabelFrame(scrollable_frame, text=f"📋 {theme_name}", font=("微软雅黑", 12, "bold"))
        theme_frame.pack(fill="x", padx=5, pady=5)
        
        # 创建两列布局
        columns_frame = tk.Frame(theme_frame, bg="white")
        columns_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 对策列
        tactics_frame = tk.LabelFrame(columns_frame, text="🎯 对策", font=("微软雅黑", 11, "bold"))
        tactics_frame.pack(side="left", fill="both", expand=True, padx=5)
        
        for i, tactic in enumerate(tactics["对策"], 1):
            tk.Label(tactics_frame, text=f"{i}. {tactic}", font=("微软雅黑", 10), fg="#27AE60").pack(anchor="w", padx=10, pady=2)
        
        # 反制列
        counters_frame = tk.LabelFrame(columns_frame, text="⚔️ 反制", font=("微软雅黑", 11, "bold"))
        counters_frame.pack(side="right", fill="both", expand=True, padx=5)
        
        for i, counter in enumerate(tactics["反制"], 1):
            tk.Label(counters_frame, text=f"{i}. {counter}", font=("微软雅黑", 10), fg="#E74C3C").pack(anchor="w", padx=10, pady=2)
    
    # 放置滚动组件
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # 控制台输出
    print("\n📋 主题对策反制:")
    print("-" * 50)
    
    for theme_name, tactics in theme_tactics.items():
        print(f"\n🎯 {theme_name}")
        print("  对策:")
        for i, tactic in enumerate(tactics["对策"], 1):
            print(f"    {i}. {tactic}")
        print("  反制:")
        for i, counter in enumerate(tactics["反制"], 1):
            print(f"    {i}. {counter}")
    
    print("\n" + "=" * 50)
    print("✅ 对策反制测试完成")
    print("📝 每个主题都有8个对策和8个反制")
    
    root.mainloop()

if __name__ == "__main__":
    test_tactics()
