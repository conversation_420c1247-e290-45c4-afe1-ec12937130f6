# -*- coding: utf-8 -*-

import os

def debug_themes():
    """调试主题名称"""
    
    print("🔍 调试主题名称")
    print("=" * 50)
    
    # 检查生命力量文件夹中的文件
    life_folder = "组合能量/生命力量"
    if os.path.exists(life_folder):
        files = [f for f in os.listdir(life_folder) if f.endswith('.json')]
        print(f"\n📁 生命力量文件夹中的文件:")
        for file in files:
            theme_name = file.replace('.json', '')
            print(f"  文件: {file} → 主题名: '{theme_name}'")
    
    # 程序中定义的主题
    theme_tactics = {
        "生命受威胁": "✅",
        "安全有隐患": "✅", 
        "地位不稳定": "✅",
        "发展受阻碍": "✅",
        "错失良机": "✅",
        "健康出问题": "✅",
        "底线遭突破": "✅",
        "退路被切断": "✅",
        "潜力被压制": "✅",
        "任期将结束": "✅",
        "权威受挑战": "✅",
        "权力被削弱": "✅",
        "影响力下降": "✅",
        "威望受损": "✅",
        "声望被诋毁": "✅",
        "遭遇信任危机": "✅",
        "荣誉被玷污": "✅",
        "丧失主动权": "✅",
        "人格魅力失效": "✅",
        "信念动摇": "✅"
    }
    
    print(f"\n📋 程序中定义的主题:")
    for theme_name in theme_tactics.keys():
        print(f"  '{theme_name}'")
    
    # 检查匹配情况
    print(f"\n🔍 匹配检查:")
    if os.path.exists(life_folder):
        files = [f for f in os.listdir(life_folder) if f.endswith('.json')]
        for file in files:
            theme_name = file.replace('.json', '')
            if theme_name in theme_tactics:
                print(f"  ✅ '{theme_name}' - 匹配")
            else:
                print(f"  ❌ '{theme_name}' - 不匹配")
    
    print("\n" + "=" * 50)
    print("🎯 调试完成")

if __name__ == "__main__":
    debug_themes()
