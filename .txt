# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
from tkinter import messagebox
import json
import os
import datetime
import random
import math

# --- 段落操作页面 (由我们之前的段落生成器重构而来) ---

class ParagraphOperationsPage(ttk.Frame):
    """
    一个独立的页面，包含了完整的段落场景设计功能。
    """
    def __init__(self, parent, status_var):
        super().__init__(parent)
        self.status_var = status_var

        # --- 配置 ---
        self.SAVE_DIRECTORY = "单独保存文件"
        self.DEFAULT_PARAGRAPH_COUNT = 32
        self.FONT_NORMAL = ("Microsoft YaHei", 10)
        self.FONT_BOLD = ("Microsoft YaHei", 10, "bold")
        self.GREEN_BUTTON_BG = "#90EE90"
        self.BLUE_BUTTON_BG = "#AACCFF"
        self.YELLOW_BUTTON_BG = "#FFFFCC"

        # --- 变量 ---
        self.paragraph_count_var = tk.IntVar(value=self.DEFAULT_PARAGRAPH_COUNT)
        self.mode_var = tk.StringVar()
        self.outline_entry = None

        self.code_entry_widgets = []
        self.paragraph_widgets = {}
        self.word_count_labels = {}
        self._status_clear_job = None
        
        self.modes = [
            "氛围：环境心理", "走动：环境行动", "情节：情节叙述", "商议：心理对话", "心理：心理反应", "策略：心理行动",
            "沟通：对话交流", "争吵：对话反应", "对峙：对话行动", "打斗：行动反应", "行动：行动叙述", "行为：行动感知"
        ]
        self.para_counts_options = [16, 32, 64, 96]
        self.factions = ["主角阵营", "正派阵营", "反派阵营", "中立阵营", "群演阵营", "怪物阵营"]

        # --- UI 创建 ---
        self._create_ui()

    def _create_ui(self):
        """创建主界面"""
        # 因为这是一个Frame，所以父容器是self
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        top_button_frame = ttk.Frame(main_frame)
        top_button_frame.pack(pady=(0, 10), fill=tk.X)

        tk.Button(
            top_button_frame, text="手动保存当前内容", command=self._save_data,
            bg=self.GREEN_BUTTON_BG, font=self.FONT_BOLD
        ).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self._create_content_section(main_frame)
        self._create_code_section(main_frame)
        
    def _create_content_section(self, parent):
        self.content_frame = ttk.LabelFrame(parent, text="段落内容", padding="10")
        self.content_frame.pack(fill=tk.BOTH, expand=True)

        outline_frame = ttk.LabelFrame(self.content_frame, text="大纲/场景标题", padding=5)
        outline_frame.pack(fill=tk.X, pady=(0, 10))
        self.outline_entry = ttk.Entry(outline_frame, font=(self.FONT_NORMAL[0], 11))
        self.outline_entry.pack(fill=tk.X, expand=True)

        self._create_character_input_section(self.content_frame)

        allocation_controls_frame = ttk.Frame(self.content_frame)
        allocation_controls_frame.pack(fill=tk.X, pady=10)

        tk.Button(
            allocation_controls_frame, text="=> 智能分配角色 <=", command=self._intelligently_allocate_characters,
            bg=self.YELLOW_BUTTON_BG, font=self.FONT_BOLD, relief=tk.RAISED, borderwidth=3
        ).pack(fill=tk.X, expand=True, ipady=5)
        
        controls_frame = ttk.Frame(self.content_frame)
        controls_frame.pack(fill=tk.X, pady=(5, 5))
        
        count_frame = ttk.Frame(controls_frame)
        count_frame.pack(fill=tk.X)
        ttk.Label(count_frame, text="段落数量:", font=self.FONT_BOLD).pack(side=tk.LEFT, padx=(0, 10))
        for count in self.para_counts_options:
            ttk.Radiobutton(
                count_frame, text=f"{count}段", value=count,
                variable=self.paragraph_count_var, command=self._update_paragraph_view
            ).pack(side=tk.LEFT, padx=5)

        mode_frame = ttk.LabelFrame(controls_frame, text="模式选项 (点击以生成内容)")
        mode_frame.pack(fill=tk.X, pady=5)
        for i, mode in enumerate(self.modes):
            ttk.Radiobutton(
                mode_frame, text=mode, value=mode,
                variable=self.mode_var, command=self._generate_all_content
            ).grid(row=i // 6, column=i % 6, padx=5, pady=2, sticky="w")
        
        scroll_container = ttk.Frame(self.content_frame)
        scroll_container.pack(fill=tk.BOTH, expand=True)
        canvas = tk.Canvas(scroll_container)
        scrollbar = ttk.Scrollbar(scroll_container, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        self.scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 鼠标滚轮绑定到特定的canvas，而不是全局
        canvas.bind_all("<MouseWheel>", lambda event: canvas.yview_scroll(int(-1*(event.delta/120)), "units"))
        self._update_paragraph_view()

    def _create_character_input_section(self, parent):
        char_frame = ttk.LabelFrame(parent, text="角色阵营参考", padding=10)
        char_frame.pack(fill=tk.X)
        self.character_entries = {}
        for i, faction_name in enumerate(self.factions):
            ttk.Label(char_frame, text=f"{faction_name}:", font=self.FONT_BOLD).grid(row=i, column=0, padx=5, pady=2, sticky="w")
            self.character_entries[faction_name] = []
            for j in range(5):
                entry = ttk.Entry(char_frame, font=self.FONT_NORMAL)
                entry.grid(row=i, column=j+1, padx=2, pady=2, sticky="ew")
                self.character_entries[faction_name].append(entry)
            char_frame.grid_columnconfigure(j+1, weight=1)

    def _create_code_section(self, parent):
        code_frame = ttk.LabelFrame(parent, text="段落生成代码 (1个代码框控制4个动态段落)", padding="10")
        code_frame.pack(fill=tk.X, pady=(10, 0))
        self.code_entry_widgets = []
        for r in range(6):
            for c in range(3):
                entry = ttk.Entry(code_frame, font=self.FONT_NORMAL)
                entry.grid(row=r, column=c, padx=5, pady=2, sticky="ew")
                self.code_entry_widgets.append(entry)
        for i in range(3):
            code_frame.grid_columnconfigure(i, weight=1)

    def _update_paragraph_view(self):
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        self.paragraph_widgets.clear()
        self.word_count_labels.clear()
        
        num_paragraphs = self.paragraph_count_var.get()
        fixed_map = self._get_fixed_paragraph_map()
        word_count_options = ["20字", "25字", "30字", "35字", "40字", "45字"]
        
        for i in range(1, num_paragraphs + 1):
            row_frame = ttk.Frame(self.scrollable_frame, padding=(0, 2))
            row_frame.pack(fill=tk.X, expand=True)
            is_fixed = i in fixed_map
            
            ttk.Label(row_frame, text=f"段落{i:02d}:", width=8, foreground="blue" if is_fixed else "black").pack(side=tk.LEFT, padx=(0, 5))
            
            name_entry = ttk.Entry(row_frame, font=self.FONT_NORMAL, width=12)
            name_entry.pack(side=tk.LEFT, padx=2)
            
            if is_fixed:
                name_entry.config(state="disabled")

            content_entries = [ttk.Entry(row_frame, font=self.FONT_NORMAL) for _ in range(3)]
            for entry in content_entries:
                entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)
            
            self.paragraph_widgets[i] = {"name": name_entry, "content": content_entries}
            
            wc_label = ttk.Label(row_frame, text=random.choice(word_count_options), width=6)
            wc_label.pack(side=tk.LEFT, padx=5)
            self.word_count_labels[i] = wc_label
    
    def _intelligently_allocate_characters(self):
        self._update_status("正在智能分配角色...", "blue")
        character_map = {}
        for faction_name, entries in self.character_entries.items():
            active_chars_in_faction = [e.get().strip() for e in entries if e.get().strip()]
            if active_chars_in_faction:
                character_map[faction_name] = active_chars_in_faction
        
        if not character_map:
            self._update_status("请先在“角色阵营参考”中输入至少一个角色名！", "orange")
            return

        active_factions = list(character_map.keys())
        dynamic_indices = self._get_dynamic_para_indices()
        num_dynamic_paras = len(dynamic_indices)

        total_chars = sum(len(chars) for chars in character_map.values())
        if total_chars == 1:
            char_name = character_map[active_factions[0]][0]
            for i in dynamic_indices:
                self.paragraph_widgets[i]["name"].delete(0, tk.END)
                self.paragraph_widgets[i]["name"].insert(0, char_name)
            self._update_status(f"已全部填充为唯一的活跃角色: {char_name}", "green")
            return

        faction_weights = {"主角阵营": 10, "反派阵营": 10, "正派阵营": 7, "中立阵营": 5, "群演阵营": 4, "怪物阵营": 4}
        active_faction_weights = {name: faction_weights.get(name, 1) for name in active_factions}
        total_weight = sum(active_faction_weights.values())

        quotas = {}
        total_quota = 0
        for name, weight in active_faction_weights.items():
            quota = round((weight / total_weight) * num_dynamic_paras)
            quotas[name] = int(quota)
            total_quota += int(quota)

        while total_quota != num_dynamic_paras:
            diff = num_dynamic_paras - total_quota
            adjust_faction = max(active_faction_weights, key=active_faction_weights.get)
            quotas[adjust_faction] += diff
            total_quota += diff
        
        try:
            faction_sequence = self._generate_allocation_sequence(quotas, max_consecutive=2)
        except Exception as e:
            self._update_status(f"生成阵营序列失败: {e}", "red")
            return

        final_character_sequence = []
        for faction_name in faction_sequence:
            available_chars = character_map.get(faction_name)
            if available_chars:
                chosen_character = random.choice(available_chars)
                final_character_sequence.append(chosen_character)
            else:
                final_character_sequence.append(f"[{faction_name}?]")

        for i, para_index in enumerate(dynamic_indices):
            entry = self.paragraph_widgets[para_index]["name"]
            entry.delete(0, tk.END)
            entry.insert(0, final_character_sequence[i])
            
        self._update_status("角色分配完成！请点击模式选项生成内容。", "green")

    def _generate_allocation_sequence(self, quota_dict, max_consecutive=2):
        bag = []
        for item, count in quota_dict.items():
            bag.extend([item] * count)
        random.shuffle(bag) 

        result_sequence = []
        while bag:
            last_item = result_sequence[-1] if result_sequence else None
            is_restricted = False
            if len(result_sequence) >= max_consecutive - 1:
                if all(item == last_item for item in result_sequence[-(max_consecutive - 1):]):
                     is_restricted = True

            valid_choices = [item for item in bag if item != last_item] if is_restricted else bag
            
            if not valid_choices:
                valid_choices = bag

            chosen_item = random.choice(valid_choices)
            result_sequence.append(chosen_item)
            bag.remove(chosen_item)
            
        return result_sequence

    def _get_dynamic_para_indices(self):
        fixed_map = self._get_fixed_paragraph_map()
        return [i for i in range(1, self.paragraph_count_var.get() + 1) if i not in fixed_map]

    def _save_data(self):
        self._update_status("正在保存...", "blue")
        try:
            if not os.path.exists(self.SAVE_DIRECTORY):
                os.makedirs(self.SAVE_DIRECTORY)
            mode_name = self.mode_var.get() or "未指定模式"
            sanitized_mode_name = mode_name.replace("：", "-").replace(":", "-").replace("/", "_")
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{sanitized_mode_name}_{timestamp}.txt"
            filepath = os.path.join(self.SAVE_DIRECTORY, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                outline_text = self.outline_entry.get()
                f.write(f"大纲：{outline_text}\n\n")
                
                for i in range(1, self.paragraph_count_var.get() + 1):
                    widgets = self.paragraph_widgets.get(i)
                    word_count_label = self.word_count_labels.get(i)
                    if not widgets or not word_count_label: continue
                    
                    name_text = widgets["name"].get()
                    content_texts = [e.get() for e in widgets["content"]]
                    combined_text = ";".join([name_text] + content_texts)
                    word_count = word_count_label.cget("text")
                    
                    f.write(f"段落{i:02d}：{combined_text}（{word_count}）\n")

            self._update_status(f"保存成功: {filepath}", "green")
        except Exception as e:
            self._update_status(f"保存失败: {e}", "red")

    def _get_fixed_paragraph_map(self):
        fixed_map = {}
        for para_idx in [1, 33, 65]: fixed_map[para_idx] = ["空间", "情节", "叙述"]
        for para_idx in [4, 8, 12, 16, 20, 24, 28, 36, 40, 44, 48, 52, 56, 60, 68, 72, 76, 80, 84, 88, 92, 96]: fixed_map[para_idx] = ["情节", "叙述", "转折"]
        return fixed_map

    def _generate_all_content(self):
        self._update_status("正在生成内容...", "blue")
        selected_mode = self.mode_var.get()
        if not selected_mode:
            self._update_status("请先选择一个模式", "orange")
            return

        try:
            os.makedirs(os.path.join("大纲段落提示", "固定编辑框"), exist_ok=True)
            os.makedirs(os.path.join("大纲段落提示", "段落代码", selected_mode), exist_ok=True)
            os.makedirs(os.path.join("大纲段落提示", "模式编辑框"), exist_ok=True)
        except OSError as e:
            self._update_status(f"创建目录失败: {e}", "red")
            return

        word_count_options = ["20字", "25字", "30字", "35字", "40字", "45字"]
        for i in range(1, self.paragraph_count_var.get() + 1):
            if i in self.word_count_labels:
                self.word_count_labels[i].config(text=random.choice(word_count_options))
                
        fixed_map = self._get_fixed_paragraph_map()
        for para_num, content_types in fixed_map.items():
            if para_num in self.paragraph_widgets:
                para_content_entries = self.paragraph_widgets[para_num]["content"]
                for i, content_type in enumerate(content_types):
                    file_path = os.path.join("大纲段落提示", "固定编辑框", content_type, f"{content_type}.txt")
                    content_text = self._get_random_line_from_file(file_path)
                    if i < len(para_content_entries):
                        para_content_entries[i].delete(0, tk.END)
                        para_content_entries[i].insert(0, content_text)

        code_path = os.path.join("大纲段落提示", "段落代码", selected_mode, f"{selected_mode}.txt")
        if not os.path.exists(code_path):
            self._update_status(f"错误: 模式代码文件未找到 {code_path}", "red")
            return
            
        with open(code_path, 'r', encoding='utf-8') as f:
            code_lines = [line.strip() for line in f if line.strip()]
        if not code_lines:
            self._update_status(f"警告: 模式代码文件为空 {code_path}", "orange")
            code_lines = ["//"]

        for entry in self.code_entry_widgets:
            entry.delete(0, tk.END)
            entry.insert(0, random.choice(code_lines))
            
        codes_to_apply = [entry.get() for entry in self.code_entry_widgets]
        dynamic_paragraph_indices = self._get_dynamic_para_indices()
        
        for i, para_num in enumerate(dynamic_paragraph_indices):
            code_box_index = i // 4
            if code_box_index >= len(codes_to_apply): continue
            master_code_string = codes_to_apply[code_box_index]
            all_code_parts = [part.strip() for part in master_code_string.split('/')]
            start_index_in_code = (i % 4) * 3
            para_content_entries = self.paragraph_widgets[para_num]["content"]
            for j, entry in enumerate(para_content_entries):
                content_text = ""
                code_part_index = start_index_in_code + j
                if code_part_index < len(all_code_parts):
                    content_type = all_code_parts[code_part_index]
                    if content_type: 
                        content_text = self._get_random_content_from_folder(content_type)
                entry.delete(0, tk.END)
                entry.insert(0, content_text)
                
        self._update_status(f"内容已生成 (模式: “{selected_mode}”), 请点击上方按钮保存。", "green")

    def _get_random_line_from_file(self, file_path):
        if not os.path.exists(file_path): return f"[{os.path.basename(file_path)}?]"
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip()]
            return random.choice(lines) if lines else "[空文件]"
        except Exception: return "[读取错误]"

    def _get_random_content_from_folder(self, content_type):
        folder_path = os.path.join("大纲段落提示", "模式编辑框", content_type)
        if not os.path.isdir(folder_path): 
            os.makedirs(folder_path, exist_ok=True)
            return f"[{content_type}?]"
        try:
            txt_files = [f for f in os.listdir(folder_path) if f.endswith('.txt')]
            if not txt_files: return f"[{content_type}:无txt]"
            return self._get_random_line_from_file(os.path.join(folder_path, random.choice(txt_files)))
        except Exception: return f"[{content_type}:错误]"

    def _update_status(self, message, color="black", duration=5000):
        # 使用传递进来的StringVar来更新主窗口的状态栏
        if self._status_clear_job:
            self.winfo_toplevel().after_cancel(self._status_clear_job)
        self.status_var.set(f"[段落操作] {message}")
        # tk.Label没有foreground属性，所以颜色无法在这里设置
        # 颜色的变化需要主窗口的状态栏自己处理
        self._status_clear_job = self.winfo_toplevel().after(duration, lambda: self.status_var.set("准备就绪"))
        
    def _show_batch_generate_popup(self, counts):
        self._update_status("批量生成功能需要重构以适应新版工作流。", "orange")


# --- 大纲主题页面 (来自用户上传的文件) ---

class ThemePage(ttk.Frame):
    """
    代表一个独立主题页面的类。
    包含了该主题下的所有UI和逻辑。
    """
    def __init__(self, parent, theme, status_var):
        super().__init__(parent)
        self.theme = theme
        self.status_var = status_var
        
        # --- 路径和文件设置 ---
        self.theme_dir = self.theme
        self.manual_save_dir = os.path.join(self.theme_dir, "手动保存")
        self.character_dir = os.path.join(self.theme_dir, "阵营角色")
        self.autosave_file = os.path.join(self.theme_dir, "autosave.json")

        if not os.path.exists(self.manual_save_dir): os.makedirs(self.manual_save_dir)
        if not os.path.exists(self.character_dir): os.makedirs(self.character_dir)
        
        # --- 布局和数据结构定义 ---
        self.layouts = { "官场": [["亲属","配偶","恩人","情人"],["人才","商人","记者","退干"],["对手","上级","纪委","群众"],["下属","秘书","线人","警务"],["同盟","靠山","代理","智囊"]], "修仙": [["境界","功法","衍术","气运"],["炼丹","炼器","阵法","符箓"],["法宝","灵药","天材地宝","灵石"],["门派","家族","洞府","交易"],["斗法","历练","秘境","妖兽"]], "都市": [["财富","权力","人脉","信息"],["公司","对手","盟友","猎头"],["爱人","密友","家庭","前任"],["线人","侦探","社团","律师"],["邻里","网红","房产","投资"]], "科幻": [["星舰","舰队","跃迁","航路"],["外星人","AI","改造人","母星"],["机甲","基因","能源","网络"],["帝国","联邦","公司","叛军"],["遗迹","异象","黑洞","维度"]], "盗墓": [["掌眼","破局","武力","斥候"],["机关","墓道","主墓室","暗河"],["分金","定穴","风水","龙脉"],["尸变","邪祟","幻象","诅咒"],["秘宝","舆图","钥匙","笔记"]], "西幻": [["战士","法师","游侠","牧师"],["人类","精灵","矮人","兽人"],["主神","邪神","巨龙","恶魔"],["王国","教会","公会","秘社"],["遗迹","任务","宝物","预言"]], "末日": [["食物","水源","药品","燃料"],["丧尸","变异兽","恶劣天气","敌对者"],["避难所","基地","废墟","移动载具"],["异能","强化","知识","意志"],["领袖","战士","医生","技工"]], "悬疑": [["死者","现场","动机","凶器"],["法证","走访","审讯","监控"],["证人","证物","不在场证明","漏洞"],["侦探","警察","嫌疑人","法医"],["侧写","伪装","圈套","心理博弈"]], "宫斗": [["皇帝","皇后","宠妃","皇子"],["外戚","太监","前朝","宫女"],["下毒","嫁祸","笼络","眼线"],["恩宠","位份","子嗣","权势"],["宴会","贡品","禁术","传言"]], "邪神": [["调查员","学者","信徒","疯人"],["旧日之神","外神","古老种族","梦境"],["神秘事件","禁忌知识","古代遗物","预兆"],["疯狂","幻觉","异变","低语"],["疯狂病院","秘密结社","图书馆","遗忘废墟"]] }
        self.relation_layout = self.layouts.get(self.theme, [])
        
        # --- 状态变量 ---
        self.all_plot_data, self.current_relation_data = {}, {}
        self.relation_buttons, self.plot_buttons, self.method_buttons = {}, {}, {}
        self.selected_relation, self.selected_plot, self.selected_method = None, None, None
        self.autosave_timer = None
        self.levels, self.full_outline_data = [], []
        self.character_data, self.character_listboxes = {}, {}
        self.factions = ["主角", "女主", "配角", "反派", "中立", "路人"]
        
        self.left_canvas, self.level_listbox = None, None
        self.char_entry1, self.char_entry2, self.search_entry = None, None, None
        self.char1_var, self.char2_var = tk.StringVar(), tk.StringVar()

        self._create_widgets()
        self._load_all_data()
        self._load_levels()
        self._load_character_data()
        self._load_autosaved_content()

    def _load_all_data(self):
        all_relations = [cat for row in self.relation_layout for cat in row]
        loaded_count = 0
        for relation_name in all_relations:
            filepath = os.path.join(self.theme_dir, f"{relation_name}.json")
            if os.path.exists(filepath):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        self.all_plot_data[relation_name] = json.load(f)
                        loaded_count += 1
                except Exception as e: self.status_var.set(f"文件加载错误: {os.path.basename(filepath)} - {e}")
        self.status_var.set(f"主题“{self.theme}”：共{len(all_relations)}个概念，加载{loaded_count}个文件")
        
    def _load_levels(self):
        level_file = os.path.join(self.theme_dir, f"{self.theme}等级.txt")
        if os.path.exists(level_file):
            try:
                with open(level_file, 'r', encoding='utf-8') as f: self.levels = [line.strip() for line in f if line.strip()]
                self.level_listbox.delete(0, tk.END)
                for level in self.levels: self.level_listbox.insert(tk.END, level)
                self.filter_level_combo['values'] = self.levels
            except Exception as e: self.status_var.set(f"等级文件加载失败: {e}")

    def _load_character_data(self):
        for faction in self.factions:
            filepath = os.path.join(self.character_dir, f"{faction}.txt")
            self.character_data[faction] = []
            if os.path.exists(filepath):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f: self.character_data[faction] = [line.strip() for line in f if line.strip()]
                except Exception as e: self.status_var.set(f"角色文件加载失败: {os.path.basename(filepath)} - {e}")
        self._populate_character_listboxes()

    def _populate_character_listboxes(self):
        for faction, listbox in self.character_listboxes.items():
            listbox.delete(0, tk.END)
            names = self.character_data.get(faction, [])
            for name in names if names else ["(空)"]: listbox.insert(tk.END, name)

    def perform_autosave(self):
        data_to_save = { "content": self.composition_area.get("1.0", tk.END), "selected_relation": self.selected_relation, "selected_plot": self.selected_plot, "selected_method": self.selected_method, "char_entry1_text": self.char1_var.get(), "char_entry2_text": self.char2_var.get() }
        try:
            with open(self.autosave_file, 'w', encoding='utf-8') as f: json.dump(data_to_save, f, ensure_ascii=False, indent=4)
            self.status_var.set(f"[{self.theme}] 状态已于 {datetime.datetime.now().strftime('%H:%M:%S')} 自动保存")
        except Exception as e: self.status_var.set(f"[{self.theme}] 自动保存失败: {e}")

    def _schedule_autosave(self, *args):
        if self.autosave_timer: self.winfo_toplevel().after_cancel(self.autosave_timer)
        self.autosave_timer = self.winfo_toplevel().after(1500, self.perform_autosave)

    def _load_autosaved_content(self):
        if not os.path.exists(self.autosave_file): return
        try:
            with open(self.autosave_file, 'r', encoding='utf-8') as f: data = json.load(f)
            if c := data.get("content"): self.composition_area.insert("1.0", c)
            if c1 := data.get("char_entry1_text"): self.char1_var.set(c1)
            if c2 := data.get("char_entry2_text"): self.char2_var.set(c2)
            if sr := data.get("selected_relation"):
                if sr in self.relation_buttons:
                    self.relation_buttons[sr]["var"].set(1)
                    self.on_relation_clicked(sr, is_restoring=True)
                    if sp := data.get("selected_plot"):
                        if sp in self.plot_buttons:
                            self.plot_buttons[sp]["var"].set(1)
                            self.on_plot_clicked(sp, is_restoring=True)
                            if sm := data.get("selected_method"):
                                if sm in self.method_buttons:
                                    self.method_buttons[sm]["var"].set(1)
                                    self.on_method_clicked(sm, is_restoring=True)
            self.status_var.set(f"[{self.theme}] 已加载上次保存的状态")
        except Exception as e: self.status_var.set(f"[{self.theme}] 加载状态失败: {e}")
        self.composition_area.edit_modified(False)

    def _create_widgets(self):
        top_pane = tk.PanedWindow(self, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=8)
        top_pane.pack(fill="both", expand=True)
        left_container = tk.Frame(top_pane)
        top_pane.add(left_container, width=500)
        self.left_canvas = tk.Canvas(left_container, highlightthickness=0)
        scrollbar = ttk.Scrollbar(left_container, orient="vertical", command=self.left_canvas.yview)
        scrollable_frame = ttk.Frame(self.left_canvas)
        scrollable_frame.bind("<Configure>", lambda e: self.left_canvas.configure(scrollregion=self.left_canvas.bbox("all")))
        self.left_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        self.left_canvas.configure(yscrollcommand=scrollbar.set)
        self.left_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        ttk.Label(scrollable_frame, text="第一步：层级控制与生成", font=("微软雅黑", 11)).pack(anchor="w", padx=5, pady=(5,0))
        tk.Button(scrollable_frame, text="一键生成千章大纲", bg="#FF8C00", fg="white", font=("微软雅黑", 10, "bold"), command=self.generate_full_outline).pack(fill="x", padx=5, pady=5)
        self.level_listbox = tk.Listbox(scrollable_frame, font=("微软雅黑", 10), height=6)
        self.level_listbox.pack(fill="x", expand=True, padx=5)
        
        ttk.Label(scrollable_frame, text="第二步：选择核心概念", font=("微软雅黑", 11)).pack(anchor="w", padx=5, pady=(5,0))
        for row in self.relation_layout:
            row_frame = tk.Frame(scrollable_frame)
            row_frame.pack(fill="x", padx=5)
            for cat in row:
                var = tk.IntVar()
                btn = tk.Checkbutton(row_frame, text=cat, variable=var, font=("微软雅黑", 10), command=lambda c=cat: self.on_relation_clicked(c))
                btn.pack(side="left", padx=5, pady=2)
                self.relation_buttons[cat] = {"button": btn, "var": var}

        self.plot_frame_container = ttk.LabelFrame(scrollable_frame, text="第三步：选择相关情节", font=("微软雅黑", 11))
        self.plot_frame_container.pack(fill="x", padx=5, pady=5)
        self.method_frame_container = ttk.LabelFrame(scrollable_frame, text="第四步：选择主角手段", font=("微软雅黑", 11))
        self.method_frame_container.pack(fill="x", padx=5, pady=5)
        
        main_pane = tk.PanedWindow(top_pane, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=8)
        top_pane.add(main_pane)
        
        char_module = tk.LabelFrame(main_pane, text="角色模块", font=("微软雅黑", 11))
        main_pane.add(char_module, width=480)
        char_grid = tk.Frame(char_module)
        char_grid.pack(fill='both', expand=True, padx=2, pady=2)
        for i in range(2): char_grid.grid_rowconfigure(i, weight=1)
        for i in range(3): char_grid.grid_columnconfigure(i, weight=1)
        
        for i, faction in enumerate(self.factions):
            r, c = divmod(i, 3)
            f_frame = tk.Frame(char_grid)
            f_frame.grid(row=r, column=c, sticky="nsew", padx=3, pady=3)
            tk.Label(f_frame, text=faction, font=("微软雅黑", 10, "bold")).pack()
            lb_container = tk.Frame(f_frame)
            lb_container.pack(fill='both', expand=True)
            lb = tk.Listbox(lb_container, font=("微软雅黑", 10), exportselection=False, width=9)
            lb.pack(side=tk.LEFT, fill='both', expand=True)
            self.character_listboxes[faction] = lb
            sb = tk.Scrollbar(lb_container, orient="vertical", command=lb.yview)
            sb.pack(side=tk.RIGHT, fill='y')
            lb.config(yscrollcommand=sb.set)
            lb.bind("<Button-1>", self.on_character_left_click)
            lb.bind("<Button-3>", self.on_character_right_click)

        bottom_controls = tk.Frame(char_module)
        bottom_controls.pack(fill='x', pady=5, padx=3)
        tk.Button(bottom_controls, text="刷新", command=self.refresh_character_data).pack(side=tk.LEFT)
        self.search_entry = ttk.Entry(bottom_controls)
        self.search_entry.pack(side=tk.LEFT, fill='x', expand=True, padx=5)
        self.search_entry.bind("<Return>", self.perform_search)
        ttk.Button(bottom_controls, text="全局搜索", command=self.perform_search).pack(side=tk.LEFT)

        comp_frame = tk.LabelFrame(main_pane, text="大纲主创作区", font=("微软雅黑", 11))
        main_pane.add(comp_frame)
        
        filter_tools = tk.Frame(comp_frame)
        filter_tools.pack(fill="x", padx=5, pady=(0, 5))
        tk.Button(filter_tools, text="显示全部", font=("微软雅黑", 10), command=self.filter_show_all).pack(side=tk.LEFT)
        self.filter_level_combo = ttk.Combobox(filter_tools, state="readonly", font=("微软雅黑", 10), width=12)
        self.filter_level_combo.pack(side=tk.LEFT, padx=5)
        self.filter_layer_combo = ttk.Combobox(filter_tools, state="readonly", font=("微软雅黑", 10), width=8, values=[f"{i} 层" for i in range(1, 11)])
        self.filter_layer_combo.pack(side=tk.LEFT, padx=5)
        tk.Button(filter_tools, text="筛选", font=("微软雅黑", 10, "bold"), command=self.filter_by_selection).pack(side=tk.LEFT)

        insert_tools = tk.Frame(comp_frame)
        insert_tools.pack(fill="x", padx=5, pady=5)
        tk.Button(insert_tools, text="插入细纲", command=self.generate_outline_manual, bg="#4CAF50", fg="white", font=("微软雅黑", 10, "bold")).pack(side=tk.LEFT)
        tk.Label(insert_tools, text="角色1:").pack(side=tk.LEFT, padx=(10,0))
        self.char_entry1 = tk.Entry(insert_tools, font=("微软雅黑", 10), textvariable=self.char1_var)
        self.char_entry1.pack(side=tk.LEFT, fill='x', expand=True, padx=(0,5))
        tk.Label(insert_tools, text="角色2:").pack(side=tk.LEFT)
        self.char_entry2 = tk.Entry(insert_tools, font=("微软雅黑", 10), textvariable=self.char2_var)
        self.char_entry2.pack(side=tk.LEFT, fill='x', expand=True)

        comp_tools = tk.Frame(comp_frame)
        comp_tools.pack(fill="x", padx=5, pady=5)
        for text, cmd in [("清空选择", self.clear_inputs), ("保存创作", self.save_composition), ("清空创作区", self.clear_composition), ("复制内容", self.copy_composition)]:
            tk.Button(comp_tools, text=text, font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=5)
        self.composition_area = tk.Text(comp_frame, relief="solid", borderwidth=1, wrap="word", font=("微软雅黑", 11), undo=True)
        self.composition_area.pack(fill="both", expand=True)
        
        self.composition_area.bind("<<Modified>>", self._schedule_autosave)
        self.char1_var.trace_add("write", self._schedule_autosave)
        self.char2_var.trace_add("write", self._schedule_autosave)

    def refresh_character_data(self): self._load_character_data(); self.status_var.set(f"[{self.theme}] 阵营角色已刷新")
        
    def _insert_character_name(self, name):
        if not name: return
        widget = self.winfo_toplevel().focus_get()
        text = f"{name}、"
        if widget == self.char_entry1: self.char1_var.set(text)
        elif widget == self.char_entry2: self.char2_var.set(text)
        else: self.composition_area.insert(tk.INSERT, text)
        self.status_var.set(f"角色“{name}”已插入")

    def _get_char_name_from_event(self, event):
        lb = event.widget
        idx = lb.nearest(event.y)
        if idx < 0: return None
        lb.selection_clear(0, tk.END); lb.selection_set(idx); lb.activate(idx)
        return None if (name := lb.get(idx)) == "(空)" else name

    def on_character_left_click(self, event): self._insert_character_name(self._get_char_name_from_event(event))
    def on_character_right_click(self, event): self.composition_area.insert(tk.INSERT, f"{(name := self._get_char_name_from_event(event))}、") if name else None

    def perform_search(self, event=None):
        term = self.search_entry.get().strip()
        if not term: return
        win = tk.Toplevel(self); win.title(f"“{term}”的搜索结果"); win.geometry("700x500")
        txt = tk.Text(win, wrap="word", font=("微软雅黑", 10), spacing1=2, spacing3=2)
        txt.pack(fill="both", expand=True, padx=10, pady=10)
        txt.tag_configure("h1", font=("微软雅黑", 12, "bold"), foreground="#00008B")
        txt.tag_configure("h2", font=("微软雅黑", 10, "bold"), foreground="#228B22")
        txt.tag_configure("hl", background="yellow")
        count = 0
        for rel, data in self.all_plot_data.items():
            hits = []
            for p_type in ["好情节", "坏情节"]:
                for plot, methods in data.get(p_type, {}).items():
                    m_hits = [f"    - {m_type}: {m}\n" for m_type in ["好手段", "坏手段"] for m in methods.get(m_type, []) if term.lower() in m.lower()]
                    if m_hits or term.lower() in plot.lower():
                        hits.append(f"  - 情节: {plot}\n"); hits.extend(m_hits)
            if hits or term.lower() in rel.lower():
                count += 1; txt.insert(tk.END, f"关系: {rel}\n", "h1")
                for line in hits: txt.insert(tk.END, line, "h2" if line.strip().startswith("- 情节:") else None)
                txt.insert(tk.END, "\n")
        if count == 0: txt.insert(tk.END, "未找到结果。")
        self.highlight_text(txt, term, "hl"); txt.config(state="disabled")

    def highlight_text(self, widget, keyword, tag):
        start = "1.0"
        while True:
            pos = widget.search(keyword, start, stopindex=tk.END, nocase=True)
            if not pos: break
            widget.tag_add(tag, pos, f"{pos}+{len(keyword)}c"); start = f"{pos}+{len(keyword)}c"

    def generate_full_outline(self):
        if not self.levels or not self.all_plot_data: self.status_var.set("无法生成: 请确保等级和情节文件存在"); return
        self.status_var.set(f"[{self.theme}] 正在生成大纲..."); self.update_idletasks()
        self.full_outline_data, recent_concepts, history_size = [], [], 3
        num_levels = len(self.levels)
        for l_idx, l_name in enumerate(self.levels):
            self.full_outline_data.append(f"# 第 {l_idx + 1} 卷：{l_name}篇\n\n")
            for i in range(1, 101):
                c_num = l_idx * 100 + i
                is_promo = (i == 100 and l_idx + 1 < num_levels) or (i % 10 == 0)
                rel, plot, method, tag = self.story_engine_get_event(l_idx, i, exclude_concepts=recent_concepts) if not is_promo else (None,)*4
                if not plot:
                    is_major = (i == 100 and l_idx + 1 < num_levels)
                    promo_txt = f"晋升{self.levels[l_idx+1]}！" if is_major else f"晋升{l_name}{i // 10}层"
                    rel, plot, method, _ = self.story_engine_get_promotion_event(l_idx, exclude_concepts=recent_concepts)
                    title, detail = (f"【晋升】{promo_txt}", f"{rel}（）{plot}；（）{method}") if plot else (f"【修炼】瓶颈期", "闭关修炼（）")
                else: title, detail = f"{tag}{plot}", f"{rel}（）{plot}；（）{method}"
                self.full_outline_data.append(f"## 第 {c_num} 章：{title}- 细纲：{detail}\n\n")
                if rel: recent_concepts.append(rel); recent_concepts = recent_concepts[-history_size:]
        self.filter_show_all(); self.status_var.set("千章大纲已生成！请使用筛选按钮查看")

    def story_engine_get_event(self, l_idx, c_idx, exclude_concepts=[]):
        for _ in range(10):
            try:
                relations = [r for r in self.all_plot_data if r not in exclude_concepts] or list(self.all_plot_data.keys())
                if not relations: return (None,)*4
                rel = random.choice(relations)
                p_type = "好情节" if random.random() < 0.6 else "坏情节"
                plots = self.all_plot_data[rel].get(p_type)
                if not plots: continue
                plot = random.choice(list(plots.keys()))
                m_type = ("好手段" if p_type == "好情节" else "坏手段") if random.random() < 0.8 else ("坏手段" if p_type == "好情节" else "好手段")
                methods = plots[plot].get(m_type) or plots[plot].get("好手段" if m_type == "坏手段" else "坏手段")
                if not methods: continue
                return rel, plot, random.choice(methods), "【奇遇】" if p_type == "好情节" else "【危机】"
            except (KeyError, IndexError, TypeError): continue
        return (None,)*4

    def story_engine_get_promotion_event(self, l_idx, exclude_concepts=[]):
        for _ in range(10):
            try:
                relations = [r for r in self.all_plot_data if r not in exclude_concepts] or list(self.all_plot_data.keys())
                if not relations: return (None,)*4
                rel = random.choice(relations)
                plots = self.all_plot_data[rel].get("好情节")
                if not plots: continue
                plot = random.choice(list(plots.keys()))
                methods = plots[plot].get("好手段")
                if not methods: continue
                return rel, plot, random.choice(methods), "【晋升】"
            except (KeyError, IndexError, TypeError): continue
        return (None,)*4

    def _extract_details_for_display(self, content): return "".join([line.split("- 细纲：", 1)[1].strip() + "\n" for line in content if "- 细纲：" in line])
    def filter_show_all(self):
        if not self.full_outline_data: return
        self.composition_area.delete("1.0", tk.END)
        self.composition_area.insert("1.0", self._extract_details_for_display(self.full_outline_data))
        self.composition_area.edit_modified(False)

    def filter_by_selection(self):
        if not self.full_outline_data or not (level_str := self.filter_level_combo.get()): return
        try: l_idx = self.levels.index(level_str)
        except ValueError: return
        layer_str = self.filter_layer_combo.get()
        if not layer_str: content = self.full_outline_data[l_idx * 101 : (l_idx + 1) * 101]
        else:
            layer_idx = int(layer_str.split(" ")[0])
            start, end = 1 + (layer_idx - 1) * 10, 1 + layer_idx * 10
            content = [self.full_outline_data[l_idx * 101]] + self.full_outline_data[l_idx * 101 + start : l_idx * 101 + end]
        self.composition_area.delete("1.0", tk.END)
        self.composition_area.insert("1.0", self._extract_details_for_display(content))
        self.composition_area.edit_modified(False)

    def on_relation_clicked(self, cat, is_restoring=False):
        is_on = self.relation_buttons[cat]["var"].get() == 1
        if not is_restoring: [d["var"].set(0) for c, d in self.relation_buttons.items() if c != cat]
        self.selected_relation = cat if is_on else None
        self.current_relation_data = self.all_plot_data.get(cat, {}) if is_on else {}
        self.selected_plot, self.selected_method = None, None
        if not is_restoring: self._schedule_autosave()
        self._update_plot_ui(); self._update_method_ui(None)

    def _create_checkbutton_grid(self, parent, data, btn_dict, cmd):
        if not data: return
        for i, item in enumerate(list(data)):
            var = tk.IntVar()
            btn = tk.Checkbutton(parent, text=item, variable=var, font=("微软雅黑", 10), command=lambda t=item: cmd(t))
            btn.grid(row=i // 4, column=i % 4, sticky="w")
            btn_dict[item] = {"button": btn, "var": var}
    
    def _update_left_scrollregion(self): self.left_canvas.configure(scrollregion=self.left_canvas.bbox("all"))

    def _update_plot_ui(self):
        [w.destroy() for w in self.plot_frame_container.winfo_children()]; self.plot_buttons.clear()
        if g_plots := self.current_relation_data.get("好情节"):
            f = tk.LabelFrame(self.plot_frame_container, text="好情节", fg="green")
            f.pack(fill="x", padx=5, pady=2)
            self._create_checkbutton_grid(f, g_plots.keys(), self.plot_buttons, self.on_plot_clicked)
        if b_plots := self.current_relation_data.get("坏情节"):
            f = tk.LabelFrame(self.plot_frame_container, text="坏情节", fg="red")
            f.pack(fill="x", padx=5, pady=2)
            self._create_checkbutton_grid(f, b_plots.keys(), self.plot_buttons, self.on_plot_clicked)
        self.after(10, self._update_left_scrollregion)

    def on_plot_clicked(self, plot, is_restoring=False):
        is_on = self.plot_buttons[plot]["var"].get() == 1
        if not is_restoring: [d["var"].set(0) for p, d in self.plot_buttons.items() if p != plot]
        self.selected_plot = plot if is_on else None
        self.selected_method = None
        if not is_restoring: self._schedule_autosave()
        self._update_method_ui(self.selected_plot)

    def _update_method_ui(self, plot):
        [w.destroy() for w in self.method_frame_container.winfo_children()]; self.method_buttons.clear()
        if not plot: return
        p_data = self.current_relation_data.get("好情节", {}).get(plot) or self.current_relation_data.get("坏情节", {}).get(plot, {})
        if g_methods := p_data.get("好手段"):
            f = tk.LabelFrame(self.method_frame_container, text="好手段", fg="green")
            f.pack(fill="x", padx=5, pady=2)
            self._create_checkbutton_grid(f, g_methods, self.method_buttons, self.on_method_clicked)
        if b_methods := p_data.get("坏手段"):
            f = tk.LabelFrame(self.method_frame_container, text="坏手段", fg="red")
            f.pack(fill="x", padx=5, pady=2)
            self._create_checkbutton_grid(f, b_methods, self.method_buttons, self.on_method_clicked)
        self.after(10, self._update_left_scrollregion)

    def on_method_clicked(self, method, is_restoring=False):
        is_on = self.method_buttons[method]["var"].get() == 1
        if not is_restoring: [d["var"].set(0) for m, d in self.method_buttons.items() if m != method]
        self.selected_method = method if is_on else None
        if not is_restoring: self._schedule_autosave()

    def generate_outline_manual(self):
        if not all([self.selected_relation, self.selected_plot, self.selected_method]): self.status_var.set("插入失败: 请完整选择“概念”、“情节”和“手段”"); return
        c1, c2 = self.char1_var.get().strip("、"), self.char2_var.get().strip("、")
        detail = f"{self.selected_relation}（{c1}）{self.selected_plot}；（{c2}）{self.selected_method}"
        self.composition_area.insert(tk.INSERT, f"{detail}\n"); self.status_var.set("已插入带角色的细纲")
        self.char1_var.set(""); self.char2_var.set("")

    def clear_inputs(self):
        if self.selected_relation and self.relation_buttons.get(self.selected_relation): self.relation_buttons[self.selected_relation]["var"].set(0)
        self.on_relation_clicked(self.selected_relation or "", is_restoring=True); self.status_var.set("已清空所有选择")

    def clear_composition(self): self.composition_area.delete('1.0', tk.END); self.status_var.set("创作区已清空")
    def copy_composition(self):
        if content := self.composition_area.get('1.0', tk.END).strip():
            self.clipboard_clear(); self.clipboard_append(content); self.status_var.set("已复制到剪贴板")
        else: self.status_var.set("创作区为空")
    def save_composition(self):
        if not (content := self.composition_area.get("1.0", tk.END).strip()): self.status_var.set("创作区为空"); return
        filepath = os.path.join(self.manual_save_dir, f"{datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.txt")
        try:
            with open(filepath, 'w', encoding='utf-8') as f: f.write(content)
            self.status_var.set(f"已手动保存到: {filepath}")
        except Exception as e: self.status_var.set(f"保存失败: {e}")

    def on_closing(self): self.perform_autosave()

# --- 主程序 ---

class MultiThemeApp:
    def __init__(self, root):
        self.root = root
        self.root.title("小说细纲与场景设计工具 v21.0 (融合版)")
        self.root.geometry("1800x900")
        self.status_var = tk.StringVar(value="请选择一个主题开始创作...")
        
        style = ttk.Style()
        style.configure("TNotebook.Tab", font=("微软雅黑", 11, "bold"), padding=[10, 5])
        style.configure("small.TButton", font=("微软雅黑", 8))
        
        self.notebook = ttk.Notebook(self.root, style="TNotebook")
        self.notebook.pack(expand=True, fill='both', padx=5, pady=5)
        
        # --- 融合修改: 将段落操作添加为第一个页面 ---
        paragraph_page = ParagraphOperationsPage(self.notebook, self.status_var)
        self.notebook.add(paragraph_page, text=" 段落操作 ")
        # --- 融合修改结束 ---
        
        self.pages = {}
        self.all_themes = ["官场", "修仙", "都市", "科幻", "盗墓", "西幻", "末日", "悬疑", "宫斗", "邪神"]
        
        for theme in self.all_themes:
            page = ThemePage(self.notebook, theme, self.status_var)
            self.pages[theme] = page
            self.notebook.add(page, text=f' {theme} ')
            
        status_bar = tk.Label(self.root, textvariable=self.status_var, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def on_closing(self):
        active_tab_index = self.notebook.index(self.notebook.select())
        # 如果是段落操作页面，它没有on_closing，所以要判断
        # 第一个tab是段落操作，后面的才是主题页
        if active_tab_index > 0:
             # 从1开始的主题页，需要减1来匹配pages字典的0-based索引
            theme_name = self.all_themes[active_tab_index - 1]
            if page := self.pages.get(theme_name):
                 page.on_closing()

        self.root.destroy()

if __name__ == "__main__":
    # 确保主题文件夹存在
    all_themes = ["官场", "修仙", "都市", "科幻", "盗墓", "西幻", "末日", "悬疑", "宫斗", "邪神"]
    for theme in all_themes:
        if not os.path.exists(theme): os.makedirs(theme)
        char_dir = os.path.join(theme, "阵营角色")
        if not os.path.exists(char_dir): os.makedirs(char_dir)
            
    root = tk.Tk()
    app = MultiThemeApp(root)
    root.mainloop()