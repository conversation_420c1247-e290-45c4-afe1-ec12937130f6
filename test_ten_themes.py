# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import os

def test_ten_themes():
    """测试10个主题的显示"""
    
    root = tk.Tk()
    root.title("测试10个主题显示")
    root.geometry("1200x800")
    
    print("🔍 测试10个主题显示")
    print("=" * 50)
    
    # 检查测试选项文件夹
    test_theme_folder = os.path.join("组合能量", "测试选项")
    
    if not os.path.exists(test_theme_folder):
        print(f"❌ 文件夹不存在: {test_theme_folder}")
        return
    
    print(f"✅ 文件夹存在: {test_theme_folder}")
    
    # 获取主题文件列表
    theme_files = []
    for file in os.listdir(test_theme_folder):
        if file.endswith('.json') or file.endswith('.jsoN'):
            theme_name = file.replace('.json', '').replace('.jsoN', '')
            theme_files.append(theme_name)
    
    theme_files.sort()
    
    print(f"📋 找到 {len(theme_files)} 个主题文件:")
    for i, theme in enumerate(theme_files, 1):
        print(f"   {i:2d}. {theme}")
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🔍 10个主题显示测试", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=10)
    
    # 说明
    desc_text = f"在'测试选项'文件夹中找到 {len(theme_files)} 个主题文件，应该全部显示出来"
    desc_label = tk.Label(
        main_frame,
        text=desc_text,
        font=("微软雅黑", 12),
        bg="white",
        fg="#666666"
    )
    desc_label.pack(pady=5)
    
    # 创建水平分割，模拟对手和主角模块
    modules_pane = tk.PanedWindow(main_frame, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=8)
    modules_pane.pack(fill="both", expand=True)
    
    # 选择变量
    opponent_energy_var = tk.StringVar()
    opponent_theme_var = tk.StringVar()
    protagonist_energy_var = tk.StringVar()
    protagonist_theme_var = tk.StringVar()
    
    def create_module_test(parent, title, energy_var, theme_var, color):
        """创建模块测试界面"""
        module_frame = tk.LabelFrame(parent, text=title, font=("微软雅黑", 14, "bold"), bg="white")
        modules_pane.add(module_frame, minsize=550)
        
        # 第一步：选择能量（文件夹）
        energy_frame = tk.LabelFrame(module_frame, text="1. 选择能量 (文件夹)", font=("微软雅黑", 11, "bold"), bg="white")
        energy_frame.pack(fill="x", padx=10, pady=5)
        
        # 显示能量文件夹选项
        energy_folders = ["生命力量", "精神力量", "思维力量", "物资资源", "信息资源", "关系资源", "测试选项"]
        
        energy_grid = tk.Frame(energy_frame, bg="white")
        energy_grid.pack(fill="x", padx=5, pady=5)
        
        for i, folder in enumerate(energy_folders):
            rb = tk.Radiobutton(
                energy_grid,
                text=folder,
                variable=energy_var,
                value=folder,
                command=lambda f=folder: update_themes(f, theme_var, theme_frame),
                font=("微软雅黑", 10),
                anchor="w",
                bg="white"
            )
            rb.grid(row=i//4, column=i%4, sticky="w", padx=5, pady=2)
        
        # 第二步：选择主题（文件）
        theme_frame = tk.LabelFrame(module_frame, text="2. 选择主题 (文件) - 应显示10个", font=("微软雅黑", 11, "bold"), bg="white", fg=color)
        theme_frame.pack(fill="x", padx=10, pady=5)
        
        # 当前选择显示
        current_frame = tk.Frame(module_frame, bg="white")
        current_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(current_frame, text="当前选择:", font=("微软雅黑", 10, "bold"), bg="white").pack(side="left")
        current_label = tk.Label(current_frame, text="", font=("微软雅黑", 10), fg="blue", bg="white")
        current_label.pack(side="left", padx=10)
        
        def on_theme_select():
            selected = theme_var.get()
            current_label.config(text=selected)
            print(f"{title} 选择了主题: {selected}")
        
        theme_var.trace('w', lambda *args: on_theme_select())
        
        return theme_frame
    
    def update_themes(energy_folder, theme_var, theme_frame):
        """更新主题显示"""
        # 清空现有内容
        for widget in theme_frame.winfo_children():
            widget.destroy()
        
        theme_var.set("")
        
        if energy_folder != "测试选项":
            tk.Label(theme_frame, text=f"选择了 {energy_folder}，但这里只测试'测试选项'", 
                    font=("微软雅黑", 10), fg="orange", bg="white").pack(pady=10)
            return
        
        # 显示测试选项的主题
        full_path = os.path.join("组合能量", energy_folder)
        
        try:
            themes = sorted([f.replace('.json', '').replace('.jsoN', '') for f in os.listdir(full_path) 
                           if f.endswith('.json') or f.endswith('.jsoN')])
            
            if themes:
                # 显示找到的主题数量
                count_label = tk.Label(theme_frame, text=f"找到 {len(themes)} 个主题:", 
                                     font=("微软雅黑", 10, "bold"), bg="white")
                count_label.pack(anchor="w", padx=5, pady=5)
                
                # 创建网格显示所有主题
                grid_frame = tk.Frame(theme_frame, bg="white")
                grid_frame.pack(fill="x", padx=5, pady=5)
                
                for i, theme in enumerate(themes):
                    row = i // 5  # 每行5个
                    col = i % 5
                    
                    rb = tk.Radiobutton(
                        grid_frame,
                        text=theme,
                        variable=theme_var,
                        value=theme,
                        font=("微软雅黑", 10),
                        anchor="w",
                        bg="white",
                        activebackground="#E3F2FD"
                    )
                    rb.grid(row=row, column=col, sticky="w", padx=5, pady=2)
                
                # 设置默认选择
                if themes:
                    theme_var.set(themes[0])
                
                print(f"✅ 显示了 {len(themes)} 个主题: {themes}")
            else:
                tk.Label(theme_frame, text="没有找到主题文件", fg="red", bg="white").pack(pady=10)
                
        except Exception as e:
            error_label = tk.Label(theme_frame, text=f"读取失败: {e}", fg="red", bg="white")
            error_label.pack(pady=10)
            print(f"❌ 读取主题失败: {e}")
    
    # 创建对手和主角模块
    opponent_theme_frame = create_module_test(modules_pane, "对手模块", opponent_energy_var, opponent_theme_var, "#E74C3C")
    protagonist_theme_frame = create_module_test(modules_pane, "主角模块", protagonist_energy_var, protagonist_theme_var, "#3498DB")
    
    # 自动选择测试选项
    def auto_select_test():
        print("🚀 自动选择'测试选项'进行测试...")
        opponent_energy_var.set("测试选项")
        protagonist_energy_var.set("测试选项")
        update_themes("测试选项", opponent_theme_var, opponent_theme_frame)
        update_themes("测试选项", protagonist_theme_var, protagonist_theme_frame)
        print("✅ 自动选择完成")
    
    # 延迟执行自动选择
    root.after(1000, auto_select_test)
    
    # 底部信息
    info_frame = tk.Frame(main_frame, bg="white")
    info_frame.pack(fill="x", pady=10)
    
    info_text = f"✅ 测试完成！应该看到 {len(theme_files)} 个主题全部展示"
    info_label = tk.Label(
        info_frame,
        text=info_text,
        font=("微软雅黑", 12, "bold"),
        bg="white",
        fg="#27AE60"
    )
    info_label.pack()
    
    print("=" * 50)
    print("✅ 测试界面创建完成")
    print("📝 将自动选择'测试选项'并显示所有主题")
    
    root.mainloop()

if __name__ == "__main__":
    test_ten_themes()
