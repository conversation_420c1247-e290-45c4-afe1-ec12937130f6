# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import json

def test_situations_display():
    """测试境况选项的全展示效果（无滚动条）"""
    
    root = tk.Tk()
    root.title("境况选项全展示测试（无滚动条）")
    root.geometry("1600x1000")
    
    # 加载测试境况数据
    test_situations = {}
    try:
        with open("测试.json", 'r', encoding='utf-8') as f:
            test_situations = json.load(f)
    except Exception as e:
        print(f"加载测试.json失败: {e}")
        return
    
    print("🎯 测试境况选项全展示（无滚动条）")
    print("=" * 60)
    
    # 统计数据
    ji_yu_count = len(test_situations.get('机遇', []))
    wei_ji_count = len(test_situations.get('危机', []))
    total_situations = ji_yu_count + wei_ji_count
    
    print(f"📊 境况数据统计:")
    print(f"   - 机遇境况: {ji_yu_count} 个")
    print(f"   - 危机境况: {wei_ji_count} 个")
    print(f"   - 总计境况: {total_situations} 个")
    
    # 计算总的选项数量
    total_options = 0
    for situation_type in ['机遇', '危机']:
        for item in test_situations.get(situation_type, []):
            total_options += len(item.get("levels", {}))
    
    print(f"   - 总选项数: {total_options} 个")
    print("=" * 60)
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🎯 角色模块第二步：境况选项全展示测试", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=10)
    
    # 说明
    desc_label = tk.Label(
        main_frame,
        text=f"所有 {total_situations} 个境况的 {total_options} 个选项全部直接展示，无滚动条",
        font=("微软雅黑", 12),
        bg="white",
        fg="#666666"
    )
    desc_label.pack(pady=5)
    
    # 创建水平分割，模拟对手和主角模块
    modules_pane = tk.PanedWindow(main_frame, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=8)
    modules_pane.pack(fill="both", expand=True)
    
    # 选择变量
    opponent_situation_var = tk.StringVar()
    protagonist_situation_var = tk.StringVar()
    
    # 状态显示函数
    def update_status():
        status_text.delete("1.0", tk.END)
        status_text.insert("1.0", f"对手境况选择: {opponent_situation_var.get()}\n")
        status_text.insert(tk.END, f"主角境况选择: {protagonist_situation_var.get()}")
    
    # 创建境况展示函数
    def create_situations_display(parent_frame, title, situation_var, color):
        module_frame = tk.LabelFrame(parent_frame, text=title, font=("微软雅黑", 14, "bold"), bg="white")
        modules_pane.add(module_frame, minsize=750)
        
        # 第二步标题
        step2_label = tk.Label(
            module_frame,
            text="第二步: 选择具体境况 (全部选项直接展示)",
            font=("微软雅黑", 12, "bold"),
            bg="white",
            fg=color
        )
        step2_label.pack(pady=10)
        
        # 主要内容区域
        content_frame = tk.Frame(module_frame, bg="white")
        content_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 显示机遇境况
        if test_situations.get('机遇'):
            ji_yu_frame = tk.LabelFrame(content_frame, text="机遇境况", font=("微软雅黑", 11, "bold"), bg="white")
            ji_yu_frame.pack(fill="x", pady=5)
            
            for i, item in enumerate(test_situations['机遇']):
                name = item.get("name", "")
                
                item_frame = tk.LabelFrame(ji_yu_frame, text=name, font=("微软雅黑", 10), bg="white")
                item_frame.pack(fill="x", padx=3, pady=2)
                
                levels_frame = tk.Frame(item_frame, bg="white")
                levels_frame.pack(fill="x", padx=5, pady=3)
                
                for j, (level, text) in enumerate(item.get("levels", {}).items()):
                    rb = tk.Radiobutton(
                        levels_frame,
                        text=f"{level}: {text}",
                        variable=situation_var,
                        value=text,
                        command=update_status,
                        font=("微软雅黑", 9),
                        anchor="w",
                        wraplength=300,
                        justify="left",
                        bg="white"
                    )
                    rb.grid(row=j//2, column=j%2, sticky="w", padx=5, pady=1)
        
        # 显示危机境况
        if test_situations.get('危机'):
            wei_ji_frame = tk.LabelFrame(content_frame, text="危机境况", font=("微软雅黑", 11, "bold"), bg="white")
            wei_ji_frame.pack(fill="x", pady=5)
            
            for i, item in enumerate(test_situations['危机']):
                name = item.get("name", "")
                
                item_frame = tk.LabelFrame(wei_ji_frame, text=name, font=("微软雅黑", 10), bg="white")
                item_frame.pack(fill="x", padx=3, pady=2)
                
                levels_frame = tk.Frame(item_frame, bg="white")
                levels_frame.pack(fill="x", padx=5, pady=3)
                
                for j, (level, text) in enumerate(item.get("levels", {}).items()):
                    rb = tk.Radiobutton(
                        levels_frame,
                        text=f"{level}: {text}",
                        variable=situation_var,
                        value=text,
                        command=update_status,
                        font=("微软雅黑", 9),
                        anchor="w",
                        wraplength=300,
                        justify="left",
                        bg="white"
                    )
                    rb.grid(row=j//2, column=j%2, sticky="w", padx=5, pady=1)
    
    # 创建对手和主角模块
    create_situations_display(modules_pane, "对手模块", opponent_situation_var, "#E74C3C")
    create_situations_display(modules_pane, "主角模块", protagonist_situation_var, "#3498DB")
    
    # 底部状态显示
    status_frame = tk.LabelFrame(main_frame, text="当前选择状态", font=("微软雅黑", 12, "bold"), bg="white")
    status_frame.pack(fill="x", pady=10)
    
    status_text = tk.Text(status_frame, height=3, font=("微软雅黑", 11), bg="#F8F9FA")
    status_text.pack(fill="x", padx=10, pady=10)
    
    # 初始化状态
    update_status()
    
    # 底部信息
    info_frame = tk.Frame(main_frame, bg="white")
    info_frame.pack(fill="x", pady=5)
    
    info_label = tk.Label(
        info_frame,
        text=f"✅ 境况选项全展示完成！总计 {total_options} 个选项，无滚动条，全部直接可见",
        font=("微软雅黑", 12, "bold"),
        bg="white",
        fg="#27AE60"
    )
    info_label.pack()
    
    print("✅ 境况选项全展示测试界面创建完成！")
    print("📝 特点:")
    print(f"   - 所有 {total_situations} 个境况全部展示")
    print(f"   - 所有 {total_options} 个选项全部可见")
    print("   - 无滚动条，直接展示")
    print("   - 按机遇/危机分组显示")
    print("   - 每个境况的所有等级选项都展示")
    
    root.mainloop()

if __name__ == "__main__":
    test_situations_display()
