# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import json

def final_verification():
    """最终验证高级选项显示"""
    
    root = tk.Tk()
    root.title("最终验证：高级选项显示")
    root.geometry("1400x900")
    
    # 加载数据
    test_situations = {}
    try:
        with open("测试.json", 'r', encoding='utf-8') as f:
            test_situations = json.load(f)
    except Exception as e:
        print(f"加载测试.json失败: {e}")
        return
    
    print("🎯 最终验证：高级选项显示")
    print("=" * 60)
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🎯 最终验证：角色模块第二步全展示（包含高级选项）", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=10)
    
    # 选择变量
    situation_var = tk.StringVar()
    
    # 当前选择显示
    current_frame = tk.Frame(main_frame, bg="white")
    current_frame.pack(fill="x", pady=10)
    
    tk.Label(current_frame, text="当前选择:", font=("微软雅黑", 12, "bold"), bg="white").pack(side="left")
    current_label = tk.Label(current_frame, text="", font=("微软雅黑", 11), fg="blue", bg="white")
    current_label.pack(side="left", padx=10)
    
    # 高级选项计数器
    high_level_selected = tk.StringVar(value="0")
    high_counter_label = tk.Label(current_frame, text="", font=("微软雅黑", 11), fg="red", bg="white")
    high_counter_label.pack(side="right", padx=10)
    
    def on_select():
        selected = situation_var.get()
        current_label.config(text=selected)
        
        if "高" in selected:
            high_level_selected.set("1")
            high_counter_label.config(text="✅ 已选择高级选项！")
            print(f"✅ 用户选择了高级选项: {selected}")
        else:
            high_level_selected.set("0")
            high_counter_label.config(text="")
            print(f"选择了: {selected}")
    
    # 主要内容区域
    content_frame = tk.Frame(main_frame, bg="white")
    content_frame.pack(fill="both", expand=True)
    
    # 机遇境况展示
    ji_yu_frame = tk.LabelFrame(content_frame, text="机遇境况 - 每个都有高级选项", font=("微软雅黑", 12, "bold"), bg="white")
    ji_yu_frame.pack(fill="x", pady=5)
    
    high_count_ji_yu = 0
    for i, item in enumerate(test_situations.get('机遇', [])):
        name = item.get("name", "")
        levels = item.get("levels", {})
        
        # 检查是否有高级选项
        has_high = "高级" in levels
        if has_high:
            high_count_ji_yu += 1
        
        # 创建境况框架
        frame_title = f"{name} {'(含高级)' if has_high else ''}"
        item_frame = tk.LabelFrame(ji_yu_frame, text=frame_title, font=("微软雅黑", 10), bg="white")
        item_frame.pack(fill="x", padx=5, pady=2)
        
        # 显示所有等级选项
        levels_frame = tk.Frame(item_frame, bg="white")
        levels_frame.pack(fill="x", padx=5, pady=3)
        
        for j, (level, text) in enumerate(levels.items()):
            # 特别标记高级选项
            if "高" in level:
                bg_color = "#FFE5E5"
                fg_color = "#D32F2F"
                font_weight = ("微软雅黑", 10, "bold")
            else:
                bg_color = "white"
                fg_color = "black"
                font_weight = ("微软雅黑", 10)
            
            rb = tk.Radiobutton(
                levels_frame,
                text=f"{level}: {text}",
                variable=situation_var,
                value=text,
                command=on_select,
                font=font_weight,
                anchor="w",
                bg=bg_color,
                fg=fg_color,
                activebackground="#FFCDD2"
            )
            rb.grid(row=0, column=j, sticky="w", padx=5, pady=2)
    
    # 危机境况展示
    wei_ji_frame = tk.LabelFrame(content_frame, text="危机境况 - 每个都有高级选项", font=("微软雅黑", 12, "bold"), bg="white")
    wei_ji_frame.pack(fill="x", pady=5)
    
    high_count_wei_ji = 0
    for i, item in enumerate(test_situations.get('危机', [])):
        name = item.get("name", "")
        levels = item.get("levels", {})
        
        # 检查是否有高级选项
        has_high = "高级" in levels
        if has_high:
            high_count_wei_ji += 1
        
        # 创建境况框架
        frame_title = f"{name} {'(含高级)' if has_high else ''}"
        item_frame = tk.LabelFrame(wei_ji_frame, text=frame_title, font=("微软雅黑", 10), bg="white")
        item_frame.pack(fill="x", padx=5, pady=2)
        
        # 显示所有等级选项
        levels_frame = tk.Frame(item_frame, bg="white")
        levels_frame.pack(fill="x", padx=5, pady=3)
        
        for j, (level, text) in enumerate(levels.items()):
            # 特别标记高级选项
            if "高" in level:
                bg_color = "#FFE5E5"
                fg_color = "#D32F2F"
                font_weight = ("微软雅黑", 10, "bold")
            else:
                bg_color = "white"
                fg_color = "black"
                font_weight = ("微软雅黑", 10)
            
            rb = tk.Radiobutton(
                levels_frame,
                text=f"{level}: {text}",
                variable=situation_var,
                value=text,
                command=on_select,
                font=font_weight,
                anchor="w",
                bg=bg_color,
                fg=fg_color,
                activebackground="#FFCDD2"
            )
            rb.grid(row=0, column=j, sticky="w", padx=5, pady=2)
    
    # 统计信息显示
    stats_frame = tk.LabelFrame(main_frame, text="高级选项统计验证", font=("微软雅黑", 12, "bold"), bg="white")
    stats_frame.pack(fill="x", pady=10)
    
    total_high = high_count_ji_yu + high_count_wei_ji
    stats_text = f"✅ 机遇境况中的高级选项: {high_count_ji_yu} 个\n"
    stats_text += f"✅ 危机境况中的高级选项: {high_count_wei_ji} 个\n"
    stats_text += f"🎯 总计高级选项: {total_high} 个（红色背景标记）\n"
    stats_text += f"📊 所有选项都已全展示，无滚动条，直接可见"
    
    stats_label = tk.Label(stats_frame, text=stats_text, font=("微软雅黑", 11), bg="white", justify="left")
    stats_label.pack(padx=10, pady=10)
    
    print(f"📊 最终统计:")
    print(f"   - 机遇境况高级选项: {high_count_ji_yu} 个")
    print(f"   - 危机境况高级选项: {high_count_wei_ji} 个")
    print(f"   - 总计高级选项: {total_high} 个")
    print("=" * 60)
    print("✅ 验证完成！所有高级选项都正确显示")
    print("📝 高级选项特征:")
    print("   - 红色背景标记")
    print("   - 粗体字显示")
    print("   - 文字包含'高级'")
    print("   - 全部直接展示，无滚动条")
    
    root.mainloop()

if __name__ == "__main__":
    final_verification()
