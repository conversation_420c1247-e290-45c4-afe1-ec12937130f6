# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

def test_three_steps_full():
    """测试三步全展示界面"""
    
    root = tk.Tk()
    root.title("三步全展示测试")
    root.geometry("1200x900")
    
    # 数据定义
    themes = ["生命力量", "精神力量", "思维力量", "物质资源", "信息资源", "关系资源", "测试项"]
    
    strategic_stances = [
        "对手 机遇 / 主角 机遇 (强强对抗)",
        "对手 危机 / 主角 机遇 (主角优势)",
        "对手 机遇 / 主角 危机 (主角劣势)",
        "对手 危机 / 主角 危机 (泥潭互搏)"
    ]
    
    tactical_stances = [
        "极限换伤 (主角进攻 vs 对手进攻)",
        "攻防大战 (主角进攻 vs 对手防守)",
        "生死时速 (主角防守 vs 对手进攻)",
        "各自为战 (主角防守 vs 对手防守)"
    ]
    
    # 生成组合选项
    combinations = []
    for o_theme in themes:
        for p_theme in themes:
            combinations.append(f"对手{o_theme} — 主角{p_theme}")
    
    print(f"创建三步全展示界面:")
    print(f"- 第一步: {len(combinations)} 个组合选项")
    print(f"- 第二步: {len(strategic_stances)} 个战略态势")
    print(f"- 第三步: {len(tactical_stances)} 个战术姿态")
    
    # 主框架
    main_frame = tk.Frame(root)
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(main_frame, text="组合模块 - 三步全展示", font=("微软雅黑", 16, "bold"))
    title_label.pack(pady=10)
    
    # 选择变量
    combination_var = tk.StringVar()
    strategic_var = tk.StringVar()
    tactical_var = tk.StringVar()
    
    # 当前选择显示
    status_frame = tk.Frame(main_frame)
    status_frame.pack(fill="x", pady=10)
    
    status_text = tk.Text(status_frame, height=4, font=("微软雅黑", 10))
    status_text.pack(fill="x")
    
    def update_status():
        status_text.delete("1.0", tk.END)
        status_text.insert("1.0", f"当前选择:\n")
        status_text.insert(tk.END, f"组合能量: {combination_var.get()}\n")
        status_text.insert(tk.END, f"战略态势: {strategic_var.get()}\n")
        status_text.insert(tk.END, f"战术姿态: {tactical_var.get()}")
    
    # 创建滚动区域
    canvas = tk.Canvas(main_frame)
    scrollbar = tk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas)
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # 第一步：组合能量全展示
    step1_label = tk.Label(scrollable_frame, text="第一步: 选择组合能量", font=("微软雅黑", 12, "bold"))
    step1_label.pack(anchor="w", pady=(10, 5))
    
    step1_frame = tk.Frame(scrollable_frame)
    step1_frame.pack(fill="x", pady=5)
    
    for i, combination in enumerate(combinations):
        row = i // 7
        col = i % 7
        
        rb = tk.Radiobutton(
            step1_frame,
            text=combination,
            variable=combination_var,
            value=combination,
            command=update_status,
            font=("微软雅黑", 8),
            anchor="w"
        )
        rb.grid(row=row, column=col, sticky="w", padx=3, pady=1)
    
    # 第二步：战略态势全展示
    step2_label = tk.Label(scrollable_frame, text="第二步: 选择战略态势", font=("微软雅黑", 12, "bold"))
    step2_label.pack(anchor="w", pady=(20, 5))
    
    step2_frame = tk.Frame(scrollable_frame)
    step2_frame.pack(fill="x", pady=5)
    
    for i, stance in enumerate(strategic_stances):
        rb = tk.Radiobutton(
            step2_frame,
            text=stance,
            variable=strategic_var,
            value=stance,
            command=update_status,
            font=("微软雅黑", 10),
            anchor="w",
            wraplength=400
        )
        rb.grid(row=i//2, column=i%2, sticky="w", padx=10, pady=3)
    
    # 第三步：战术姿态全展示
    step3_label = tk.Label(scrollable_frame, text="第三步: 选择战术姿态", font=("微软雅黑", 12, "bold"))
    step3_label.pack(anchor="w", pady=(20, 5))
    
    step3_frame = tk.Frame(scrollable_frame)
    step3_frame.pack(fill="x", pady=5)
    
    for i, stance in enumerate(tactical_stances):
        rb = tk.Radiobutton(
            step3_frame,
            text=stance,
            variable=tactical_var,
            value=stance,
            command=update_status,
            font=("微软雅黑", 10),
            anchor="w",
            wraplength=400
        )
        rb.grid(row=i//2, column=i%2, sticky="w", padx=10, pady=3)
    
    # 布局滚动组件
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # 鼠标滚轮支持
    def on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    canvas.bind("<MouseWheel>", on_mousewheel)
    
    # 设置默认选择
    if combinations:
        combination_var.set(combinations[0])
    if strategic_stances:
        strategic_var.set(strategic_stances[0])
    if tactical_stances:
        tactical_var.set(tactical_stances[0])
    
    update_status()
    
    print("界面创建完成！")
    print("- 所有选项都直接展示，无需下拉")
    print("- 三个步骤按顺序排列")
    print("- 支持滚动查看")
    
    root.mainloop()

if __name__ == "__main__":
    test_three_steps_full()
