# -*- coding: utf-8 -*-

import json

def test_work_json():
    """测试工作.json文件是否已简化"""
    
    print("🔍 测试工作.json文件简化状态")
    print("=" * 50)
    
    try:
        with open("组合能量/测试选项/工作.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("✅ 成功加载工作.json文件")
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return
    
    print("\n📋 工作.json文件内容:")
    print("-" * 50)
    
    # 检查机遇
    print("\n🌟 机遇:")
    for theme_obj in data.get("机遇", []):
        theme_name = theme_obj.get("theme_name", "")
        print(f"  主题名称: {theme_name}")
        
        for category in ["自主", "互动", "强制", "运气"]:
            category_data = theme_obj.get(category)
            if category_data:
                name = category_data.get("name", "")
                print(f"    {category}型 - {name}:")
                for level, text in category_data.get("levels", {}).items():
                    # 检查是否包含冒号
                    has_colon = "：" in text
                    status = "❌ 包含冒号" if has_colon else "✅ 已简化"
                    print(f"      {level}: {text} ({status})")
    
    # 检查危机
    print("\n⚠️ 危机:")
    for theme_obj in data.get("危机", []):
        theme_name = theme_obj.get("theme_name", "")
        print(f"  主题名称: {theme_name}")
        
        for category in ["自主", "互动", "强制", "运气"]:
            category_data = theme_obj.get(category)
            if category_data:
                name = category_data.get("name", "")
                print(f"    {category}型 - {name}:")
                for level, text in category_data.get("levels", {}).items():
                    # 检查是否包含冒号
                    has_colon = "：" in text
                    status = "❌ 包含冒号" if has_colon else "✅ 已简化"
                    print(f"      {level}: {text} ({status})")
    
    # 统计简化状态
    total_items = 0
    simplified_items = 0
    
    for situation_type in ["机遇", "危机"]:
        for theme_obj in data.get(situation_type, []):
            for category in ["自主", "互动", "强制", "运气"]:
                category_data = theme_obj.get(category)
                if category_data:
                    for level, text in category_data.get("levels", {}).items():
                        total_items += 1
                        if "：" not in text:
                            simplified_items += 1
    
    print("\n" + "=" * 50)
    print(f"📊 简化统计:")
    print(f"  总项目数: {total_items}")
    print(f"  已简化: {simplified_items}")
    print(f"  未简化: {total_items - simplified_items}")
    print(f"  简化率: {simplified_items/total_items*100:.1f}%")
    
    if simplified_items == total_items:
        print("✅ 工作.json文件已完全简化！")
    else:
        print("❌ 工作.json文件还有未简化的内容")
    
    print("\n🎯 现在程序应该显示简化后的内容了")

if __name__ == "__main__":
    test_work_json()
