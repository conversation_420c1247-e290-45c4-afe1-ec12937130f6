# -*- coding: utf-8 -*-

def get_complete_theme_tactics():
    """获取所有60个主题的完整对策反制"""
    
    return {
        # 生命力量 (10个)
        "生命": {
            "对策": ["金蝉脱壳", "躲入暗处", "伪造身份", "寻求公义", "假死脱身", "投靠强敌", "鱼死网破", "绑架人质"],
            "反制": ["买凶杀人", "天罗地网", "识破伪装", "官官相护", "识破诡计", "借刀杀人", "早有准备", "拒不受胁"]
        },
        "安全": {
            "对策": ["深居简出", "暗中调查", "设置陷阱", "寻求庇护", "利益交换", "雇佣保镖", "先发制人", "制造伪证"],
            "反制": ["暗中布局", "加紧监视", "隐藏更深", "釜底抽薪", "出尔反尔", "收买保镖", "早有准备", "找出破绽"]
        },
        "地位": {
            "对策": ["谨言慎行", "整顿下属", "展现能力", "拜访前辈", "向上靠拢", "平衡派系", "安插亲信", "排除异己"],
            "反制": ["煽动下属", "抓住把柄", "火上浇油", "从中作梗", "恶意中伤", "两面开火", "策反亲信", "拼死反扑"]
        },
        "发展": {
            "对策": ["做出政绩", "韬光养晦", "学习深造", "投靠派系", "利益捆绑", "请求调动", "越级上报", "强行推动"],
            "反制": ["处处掣肘", "窃取功劳", "制造障碍", "从中作梗", "破坏合作", "从中作梗", "倒打一耙", "联合抵制"]
        },
        "机会": {
            "对策": ["自我反省", "创造机会", "等待下次", "弥补关系", "寻求补偿", "交换资源", "破坏他人", "强行索取"],
            "反制": ["捷足先登", "恶意嘲讽", "从中破坏", "不屑一顾", "断然拒绝", "毫无诚意", "早有防备", "强硬抵抗"]
        },
        "健康": {
            "对策": ["遍寻名医", "修身养性", "隐瞒病情", "请求御医", "交换秘方", "求取丹药", "夺取药材", "嫁祸于人"],
            "反制": ["暗中下毒", "收买名医", "察觉异常", "从中作梗", "提供假药", "恶意羞辱", "设下埋伏", "反向调查"]
        },
        "底线": {
            "对策": ["坚守原则", "辞官退隐", "内心挣扎", "寻求支持", "仗义执言", "寻找同道", "奋起反抗", "实名举报"],
            "反制": ["威逼利诱", "残酷打压", "步步为营", "威胁支持者", "杀鸡儆猴", "安插内鬼", "强力镇压", "压下举报"]
        },
        "退路": {
            "对策": ["置之死地", "绝地反击", "另辟蹊径", "请求援助", "舍命相救", "跪求原谅", "殊死一搏", "挟持人质"],
            "反制": ["赶尽杀绝", "早有预料", "拼死抵抗", "围点打援", "一网打尽", "百般羞辱", "实力碾压", "拒不受胁"]
        },
        "潜力": {
            "对策": ["厚积薄发", "磨练心性", "自暴自弃", "良禽择木", "毛遂自荐", "得遇伯乐", "锋芒毕露", "取而代之"],
            "反制": ["打压新人", "窃取成果", "恶意解读", "从中作梗", "公开打压", "恶意中伤", "强力打压", "拼死反扑"]
        },
        "任期": {
            "对策": ["安享晚年", "最后布局", "撰写回忆", "推荐继任", "政治交易", "被挽留", "贪污敛财", "清除政敌"],
            "反制": ["提前布局", "翻出旧案", "将计就计", "从中作梗", "毫无诚意", "虚情假意", "掌握证据", "拼死反扑"]
        },
        
        # 精神力量 (10个)
        "权威": {
            "对策": ["做出表率", "坚定立场", "恪守原则", "上级认可", "收买人心", "获得支持", "杀鸡儆猴", "公开立威"],
            "反制": ["公开质疑", "指责作秀", "孤立主角", "越级告状", "将计就计", "分化瓦解", "煽动众人", "当众打脸"]
        },
        "权力": {
            "对策": ["韬光养晦", "分析局势", "蛰伏待机", "利益交换", "请求支援", "寻求联合", "集权一身", "清除异己"],
            "反制": ["釜底抽薪", "步步紧逼", "斩草除根", "毫无诚意", "从中作梗", "分化瓦解", "阳奉阴违", "拼死反扑"]
        },
        "影响力": {
            "对策": ["重塑形象", "扩大声势", "展示实力", "寻求盟友", "媒体造势", "公关活动", "强势回归", "重新定位"],
            "反制": ["恶意中伤", "封锁消息", "暗中破坏", "孤立主角", "负面宣传", "抵制活动", "强力压制", "边缘化"]
        },
        "威望": {
            "对策": ["澄清事实", "重建信任", "以退为进", "寻求证人", "公开道歉", "承担责任", "反击造谣", "重新证明"],
            "反制": ["恶意传播", "混淆视听", "拒不配合", "收买证人", "拒绝道歉", "推卸责任", "继续造谣", "质疑能力"]
        },
        "声望": {
            "对策": ["正面回应", "寻找证据", "反击造谣", "法律维权", "媒体澄清", "证人作证", "以德报怨", "重新证明"],
            "反制": ["散布谣言", "伪造证据", "继续造谣", "法律阻挠", "媒体封锁", "收买证人", "恶意报复", "质疑动机"]
        },
        "信任": {
            "对策": ["坦诚相待", "重建关系", "证明清白", "寻求理解", "承认错误", "弥补过失", "重新开始", "以行证言"],
            "反制": ["怀疑一切", "拒绝沟通", "质疑动机", "拒不理解", "拒绝原谅", "追究到底", "断绝关系", "以言代行"]
        },
        "荣誉": {
            "对策": ["维护名誉", "寻求正义", "以德报怨", "重新证明", "法律维权", "公开澄清", "坚持真相", "时间证明"],
            "反制": ["恶意玷污", "阻挠正义", "以怨报德", "质疑能力", "法律阻挠", "封锁消息", "歪曲真相", "持续打击"]
        },
        "主动权": {
            "对策": ["重新布局", "寻找机会", "主动出击", "创造条件", "改变策略", "联合盟友", "突破困局", "重夺主动"],
            "反制": ["严密控制", "封锁机会", "被动防守", "破坏条件", "维持现状", "分化盟友", "加强困局", "保持主动"]
        },
        "人格魅力": {
            "对策": ["重塑形象", "改变策略", "寻找新路", "学习提升", "真诚待人", "展示才华", "重建魅力", "以德服人"],
            "反制": ["恶意中伤", "阻挠改变", "封锁道路", "阻止提升", "虚假待人", "掩盖才华", "破坏魅力", "以力压人"]
        },
        "信念": {
            "对策": ["坚定信念", "寻找支撑", "重新定位", "学习充实", "寻求指导", "内心修炼", "重建信心", "坚持理想"],
            "反制": ["动摇信念", "破坏支撑", "混乱定位", "阻止学习", "误导方向", "干扰修炼", "打击信心", "破坏理想"]
        },
        
        # 思维力量 (10个)
        "韬略": {
            "对策": ["复盘棋局", "阅读兵法", "闭门思考", "请教高人", "招募谋士", "与人对弈", "窃取计划", "设局考验"],
            "反制": ["设下圈套", "散布歪理", "制造事端", "威胁高人", "安插内鬼", "摸清底细", "将计就计", "离心离德"]
        },
        "眼界": {
            "对策": ["外出游学", "登高望远", "研究历史", "微服私访", "跨界交流", "拜访高人", "强行破局", "夺取密卷"],
            "反制": ["信息封锁", "制造假象", "篡改历史", "提前安排", "从中作梗", "释放假消息", "早有准备", "设下陷阱"]
        },
        "城府": {
            "对策": ["深藏不露", "察言观色", "以退为进", "虚与委蛇", "借刀杀人", "声东击西", "欲擒故纵", "釜底抽薪"],
            "反制": ["直来直去", "看破心思", "步步紧逼", "真诚相待", "正面对抗", "兵来将挡", "主动出击", "加薪添火"]
        },
        "政绩": {
            "对策": ["埋头苦干", "创新突破", "学习先进", "争取资源", "团队合作", "媒体宣传", "数据造假", "抢夺功劳"],
            "反制": ["暗中破坏", "阻挠创新", "封锁信息", "切断资源", "分化团队", "负面报道", "揭露造假", "据为己有"]
        },
        "知识": {
            "对策": ["勤奋学习", "请教专家", "实地考察", "购买资料", "参加培训", "网络搜索", "窃取情报", "强迫传授"],
            "反制": ["封锁信息", "误导专家", "阻挠考察", "垄断资料", "破坏培训", "网络攻击", "反间计", "拒绝传授"]
        },
        "经验": {
            "对策": ["虚心求教", "实践锻炼", "总结反思", "拜师学艺", "交流心得", "观摩学习", "偷师学艺", "逼迫指导"],
            "反制": ["拒绝指教", "设置障碍", "误导思路", "收徒不教", "保守秘密", "阻挠观摩", "严加防范", "虚假指导"]
        },
        "大局观": {
            "对策": ["站高看远", "系统思考", "多方调研", "咨询智囊", "模拟推演", "历史借鉴", "强行参与", "窃听会议"],
            "反制": ["局限视野", "分散注意", "提供假象", "误导智囊", "干扰推演", "歪曲历史", "排斥在外", "严密保密"]
        },
        "资历": {
            "对策": ["踏实工作", "积累经验", "寻求提拔", "拉拢前辈", "展示才华", "争取机会", "伪造履历", "排挤同辈"],
            "反制": ["打压新人", "质疑能力", "阻挠提拔", "孤立主角", "掩盖才华", "封锁机会", "揭露造假", "联合排斥"]
        },
        "决策权": {
            "对策": ["证明能力", "争取信任", "寻求授权", "联合盟友", "展示成果", "请求试用", "架空上级", "逼宫夺权"],
            "反制": ["质疑决策", "破坏信任", "拒绝授权", "分化盟友", "否定成果", "拒绝试用", "加强控制", "先发制人"]
        },
        "话语权": {
            "对策": ["积极发言", "建立威信", "寻求支持", "媒体发声", "专业论证", "公开辩论", "制造舆论", "压制异见"],
            "反制": ["限制发言", "质疑威信", "孤立主角", "封锁媒体", "反驳论证", "拒绝辩论", "引导舆论", "放大异见"]
        },
        
        # 物质资源 (10个)
        "资金": {
            "对策": ["开源节流", "变卖家产", "亲力亲为", "请求拨款", "商业贷款", "寻求赞助", "挪用公款", "强行摊派"],
            "反制": ["切断财源", "恶意压价", "增加负担", "从中作梗", "施加压力", "恶意中伤", "掌握证据", "煽动众人"]
        },
        "项目": {
            "对策": ["另起炉灶", "搜集证据", "总结教训", "向上申诉", "请求仲裁", "联合他人", "公开对质", "匿名举报"],
            "反制": ["窃取功劳", "销毁证据", "恶意嘲讽", "倒打一耙", "贿赂高层", "分化瓦解", "混淆视听", "压下举报"]
        },
        "预算": {
            "对策": ["精打细算", "争取追加", "寻找替代", "申请特批", "内部调剂", "外部筹措", "虚报需求", "挪用他项"],
            "反制": ["严格审查", "拒绝追加", "封锁渠道", "驳回申请", "阻挠调剂", "切断外援", "揭露虚报", "严查挪用"]
        },
        "资产": {
            "对策": ["合法转移", "隐匿财产", "寻求保护", "法律申诉", "政治庇护", "国际转移", "暴力抗法", "同归于尽"],
            "反制": ["严密监控", "全面搜查", "内外封锁", "驳回申诉", "拒绝庇护", "国际合作", "武力镇压", "先下手为强"]
        },
        "设备": {
            "对策": ["自主研发", "技术引进", "设备租赁", "申请更新", "合作共享", "二手采购", "强行征用", "偷盗抢夺"],
            "反制": ["技术封锁", "阻挠引进", "拒绝租赁", "驳回申请", "破坏合作", "垄断市场", "严加保护", "严厉打击"]
        },
        "许可": {
            "对策": ["完善手续", "寻求帮助", "政策解读", "关系疏通", "媒体施压", "法律维权", "违规操作", "贿赂官员"],
            "反制": ["严格审查", "故意刁难", "政策收紧", "关系阻挠", "媒体封锁", "法律阻挠", "严厉查处", "拒绝贿赂"]
        },
        "编制": {
            "对策": ["申请增编", "内部调剂", "临时聘用", "外包服务", "志愿帮助", "技术替代", "超编运行", "强行安排"],
            "反制": ["冻结编制", "严格控制", "禁止聘用", "限制外包", "驱散志愿", "技术封锁", "严查超编", "坚决抵制"]
        },
        "福利": {
            "对策": ["据理力争", "集体谈判", "寻求支持", "媒体曝光", "法律维权", "上级申诉", "罢工抗议", "暴力对抗"],
            "反制": ["拒不恢复", "分化瓦解", "孤立主角", "封锁媒体", "法律压制", "上级施压", "强力镇压", "武力对抗"]
        },
        "献金": {
            "对策": ["寻找替代", "降低标准", "分期支付", "寻求减免", "集体分摊", "外部筹措", "拒绝支付", "虚假应付"],
            "反制": ["坚持要求", "提高标准", "要求一次性", "拒绝减免", "阻挠分摊", "切断外援", "强制执行", "严查造假"]
        },
        "地盘": {
            "对策": ["据理力争", "寻求仲裁", "政治解决", "联合抗争", "媒体造势", "法律维权", "武装抵抗", "同归于尽"],
            "反制": ["强行占领", "拒绝仲裁", "政治施压", "分化瓦解", "封锁媒体", "法律压制", "武力镇压", "先发制人"]
        }
    }

def update_program_with_complete_tactics():
    """更新程序文件，添加完整的60个主题对策反制"""
    
    print("🔧 生成完整的60个主题对策反制代码")
    print("=" * 50)
    
    all_tactics = get_complete_theme_tactics()
    
    # 生成代码字符串
    tactics_code = "        # 主题专用对策和反制字典\n        self.theme_tactics = {\n"
    
    for theme_name, tactics in all_tactics.items():
        tactics_code += f'            "{theme_name}": {{\n'
        tactics_code += f'                "对策": {tactics["对策"]},\n'
        tactics_code += f'                "反制": {tactics["反制"]}\n'
        tactics_code += f'            }},\n'
    
    tactics_code = tactics_code.rstrip(',\n') + '\n        }'
    
    print(f"✅ 生成了 {len(all_tactics)} 个主题的对策反制")
    print("📝 每个主题包含8个对策和8个反制")
    
    # 保存到文件供手动复制
    with open("complete_60_tactics_code.txt", "w", encoding="utf-8") as f:
        f.write(tactics_code)
    
    print("💾 完整代码已保存到 complete_60_tactics_code.txt")
    print("📋 请手动复制到程序文件中替换现有的theme_tactics")
    
    # 显示统计信息
    energy_count = {
        "生命力量": 0, "精神力量": 0, "思维力量": 0, 
        "物质资源": 0, "信息资源": 0, "关系资源": 0
    }
    
    theme_names = list(all_tactics.keys())
    life_themes = ["生命", "安全", "地位", "发展", "机会", "健康", "底线", "退路", "潜力", "任期"]
    spirit_themes = ["权威", "权力", "影响力", "威望", "声望", "信任", "荣誉", "主动权", "人格魅力", "信念"]
    mind_themes = ["韬略", "眼界", "城府", "政绩", "知识", "经验", "大局观", "资历", "决策权", "话语权"]
    material_themes = ["资金", "项目", "预算", "资产", "设备", "许可", "编制", "福利", "献金", "地盘"]
    
    for theme in theme_names:
        if theme in life_themes:
            energy_count["生命力量"] += 1
        elif theme in spirit_themes:
            energy_count["精神力量"] += 1
        elif theme in mind_themes:
            energy_count["思维力量"] += 1
        elif theme in material_themes:
            energy_count["物质资源"] += 1
    
    print(f"\n📊 各能量主题统计:")
    for energy, count in energy_count.items():
        print(f"  {energy}: {count}个主题")
    
    print(f"\n🎯 还需要添加:")
    print(f"  信息资源: 10个主题")
    print(f"  关系资源: 10个主题")
    
    return all_tactics

if __name__ == "__main__":
    update_program_with_complete_tactics()
