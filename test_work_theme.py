# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import json
import os

def test_work_theme():
    """测试工作主题的内容"""
    
    root = tk.Tk()
    root.title("工作主题内容测试")
    root.geometry("1400x900")
    
    print("🔍 测试工作主题内容")
    print("=" * 60)
    
    # 加载工作主题数据
    theme_file = os.path.join("组合能量", "测试选项", "主题A.json")
    
    try:
        with open(theme_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ 成功加载文件: {theme_file}")
    except Exception as e:
        print(f"❌ 加载文件失败: {e}")
        return
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🏢 工作主题内容展示", 
        font=("微软雅黑", 18, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=10)
    
    # 创建水平分割：机遇和危机
    pane = tk.PanedWindow(main_frame, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=8)
    pane.pack(fill="both", expand=True)
    
    def create_situation_display(parent, title, situation_data, color):
        """创建境况显示区域"""
        frame = tk.LabelFrame(parent, text=title, font=("微软雅黑", 14, "bold"), bg="white", fg=color)
        pane.add(frame, minsize=650)
        
        # 滚动框架
        canvas = tk.Canvas(frame, bg="white")
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="white")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 显示数据
        for theme_obj in situation_data:
            theme_name = theme_obj.get("theme_name", "未知主题")
            
            # 主题标题
            theme_title = tk.Label(
                scrollable_frame,
                text=f"📋 主题: {theme_name}",
                font=("微软雅黑", 12, "bold"),
                bg="white",
                fg=color
            )
            theme_title.pack(anchor="w", padx=10, pady=(10, 5))
            
            # 四种类型：自主、互动、强制、运气
            categories = ["自主", "互动", "强制", "运气"]
            
            for category in categories:
                category_data = theme_obj.get(category)
                if not category_data:
                    continue
                
                # 类型框架
                category_frame = tk.LabelFrame(
                    scrollable_frame,
                    text=f"{category}型 - {category_data.get('name', '')}",
                    font=("微软雅黑", 11, "bold"),
                    bg="white",
                    fg="#333333"
                )
                category_frame.pack(fill="x", padx=15, pady=5)
                
                # 三个等级
                levels = category_data.get("levels", {})
                for level, description in levels.items():
                    level_frame = tk.Frame(category_frame, bg="white")
                    level_frame.pack(fill="x", padx=10, pady=3)
                    
                    # 等级标签
                    level_color = "#E74C3C" if level == "高级" else "#F39C12" if level == "中级" else "#27AE60"
                    level_label = tk.Label(
                        level_frame,
                        text=f"【{level}】",
                        font=("微软雅黑", 10, "bold"),
                        bg="white",
                        fg=level_color
                    )
                    level_label.pack(side="left", padx=(0, 10))
                    
                    # 描述标签
                    desc_label = tk.Label(
                        level_frame,
                        text=description,
                        font=("微软雅黑", 10),
                        bg="white",
                        fg="#333333",
                        wraplength=500,
                        justify="left"
                    )
                    desc_label.pack(side="left", fill="x", expand=True)
        
        # 放置滚动组件
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        return frame
    
    # 创建机遇和危机显示区域
    opportunity_frame = create_situation_display(pane, "🌟 工作机遇", data.get("机遇", []), "#27AE60")
    crisis_frame = create_situation_display(pane, "⚠️ 工作危机", data.get("危机", []), "#E74C3C")
    
    # 底部统计信息
    stats_frame = tk.Frame(main_frame, bg="white")
    stats_frame.pack(fill="x", pady=10)
    
    # 统计数据
    opportunity_count = 0
    crisis_count = 0
    
    for theme_obj in data.get("机遇", []):
        for category in ["自主", "互动", "强制", "运气"]:
            if category in theme_obj:
                opportunity_count += len(theme_obj[category].get("levels", {}))
    
    for theme_obj in data.get("危机", []):
        for category in ["自主", "互动", "强制", "运气"]:
            if category in theme_obj:
                crisis_count += len(theme_obj[category].get("levels", {}))
    
    stats_text = f"📊 统计: 机遇选项 {opportunity_count} 个 | 危机选项 {crisis_count} 个 | 总计 {opportunity_count + crisis_count} 个选项"
    stats_label = tk.Label(
        stats_frame,
        text=stats_text,
        font=("微软雅黑", 12, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    stats_label.pack()
    
    # 控制台输出详细信息
    print("\n📋 工作主题详细内容:")
    print("-" * 60)
    
    for situation_type in ["机遇", "危机"]:
        print(f"\n🔸 {situation_type}:")
        for theme_obj in data.get(situation_type, []):
            theme_name = theme_obj.get("theme_name", "未知")
            print(f"  主题: {theme_name}")
            
            for category in ["自主", "互动", "强制", "运气"]:
                category_data = theme_obj.get(category)
                if category_data:
                    print(f"    {category}型 - {category_data.get('name', '')}:")
                    for level, desc in category_data.get("levels", {}).items():
                        print(f"      【{level}】{desc}")
    
    print("\n" + "=" * 60)
    print("✅ 工作主题测试完成")
    print(f"📊 机遇选项: {opportunity_count} 个")
    print(f"📊 危机选项: {crisis_count} 个")
    print(f"📊 总计选项: {opportunity_count + crisis_count} 个")
    
    root.mainloop()

if __name__ == "__main__":
    test_work_theme()
