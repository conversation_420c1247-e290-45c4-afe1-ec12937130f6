# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import json

def test_high_level_display():
    """专门测试"高级"选项是否正确显示"""
    
    root = tk.Tk()
    root.title("测试高级选项显示")
    root.geometry("1200x800")
    
    # 加载测试数据
    test_situations = {}
    try:
        with open("测试.json", 'r', encoding='utf-8') as f:
            test_situations = json.load(f)
    except Exception as e:
        print(f"加载测试.json失败: {e}")
        return
    
    print("🔍 专门测试'高级'选项显示")
    print("=" * 50)
    
    # 统计高级选项
    high_level_count = 0
    all_levels_count = 0
    
    for situation_type in ['机遇', '危机']:
        for item in test_situations.get(situation_type, []):
            levels = item.get("levels", {})
            all_levels_count += len(levels)
            if "高级" in levels:
                high_level_count += 1
    
    print(f"📊 数据分析:")
    print(f"   - 总境况数: {len(test_situations.get('机遇', [])) + len(test_situations.get('危机', []))}")
    print(f"   - 总选项数: {all_levels_count}")
    print(f"   - 高级选项数: {high_level_count}")
    print("=" * 50)
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🔍 高级选项显示测试", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#E74C3C"
    )
    title_label.pack(pady=10)
    
    # 选择变量
    situation_var = tk.StringVar()
    
    # 当前选择显示
    current_frame = tk.Frame(main_frame, bg="white")
    current_frame.pack(fill="x", pady=10)
    
    tk.Label(current_frame, text="当前选择:", font=("微软雅黑", 12, "bold"), bg="white").pack(side="left")
    current_label = tk.Label(current_frame, text="", font=("微软雅黑", 11), fg="blue", bg="white")
    current_label.pack(side="left", padx=10)
    
    def on_select():
        selected = situation_var.get()
        current_label.config(text=selected)
        if "高" in selected:
            print(f"✅ 选择了高级选项: {selected}")
        else:
            print(f"选择了: {selected}")
    
    # 创建内容区域
    content_frame = tk.Frame(main_frame, bg="white")
    content_frame.pack(fill="both", expand=True)
    
    # 显示机遇境况
    ji_yu_frame = tk.LabelFrame(content_frame, text="机遇境况 - 验证高级选项", font=("微软雅黑", 12, "bold"), bg="white")
    ji_yu_frame.pack(fill="x", pady=5)
    
    print("🔍 检查机遇境况的高级选项:")
    for i, item in enumerate(test_situations.get('机遇', [])):
        name = item.get("name", "")
        levels = item.get("levels", {})
        
        print(f"   {i+1}. {name}: {list(levels.keys())}")
        
        # 创建境况框架
        item_frame = tk.LabelFrame(ji_yu_frame, text=name, font=("微软雅黑", 10), bg="white")
        item_frame.pack(fill="x", padx=5, pady=2)
        
        # 显示所有等级选项
        levels_frame = tk.Frame(item_frame, bg="white")
        levels_frame.pack(fill="x", padx=5, pady=3)
        
        for j, (level, text) in enumerate(levels.items()):
            # 特别标记高级选项
            if "高" in level:
                bg_color = "#FFE5E5"  # 高级选项用红色背景
                fg_color = "#D32F2F"
            else:
                bg_color = "white"
                fg_color = "black"
            
            rb = tk.Radiobutton(
                levels_frame,
                text=f"{level}: {text}",
                variable=situation_var,
                value=text,
                command=on_select,
                font=("微软雅黑", 10),
                anchor="w",
                bg=bg_color,
                fg=fg_color,
                activebackground="#FFCDD2"
            )
            rb.grid(row=0, column=j, sticky="w", padx=5, pady=2)
    
    # 显示危机境况
    wei_ji_frame = tk.LabelFrame(content_frame, text="危机境况 - 验证高级选项", font=("微软雅黑", 12, "bold"), bg="white")
    wei_ji_frame.pack(fill="x", pady=5)
    
    print("🔍 检查危机境况的高级选项:")
    for i, item in enumerate(test_situations.get('危机', [])):
        name = item.get("name", "")
        levels = item.get("levels", {})
        
        print(f"   {i+1}. {name}: {list(levels.keys())}")
        
        # 创建境况框架
        item_frame = tk.LabelFrame(wei_ji_frame, text=name, font=("微软雅黑", 10), bg="white")
        item_frame.pack(fill="x", padx=5, pady=2)
        
        # 显示所有等级选项
        levels_frame = tk.Frame(item_frame, bg="white")
        levels_frame.pack(fill="x", padx=5, pady=3)
        
        for j, (level, text) in enumerate(levels.items()):
            # 特别标记高级选项
            if "高" in level:
                bg_color = "#FFE5E5"  # 高级选项用红色背景
                fg_color = "#D32F2F"
            else:
                bg_color = "white"
                fg_color = "black"
            
            rb = tk.Radiobutton(
                levels_frame,
                text=f"{level}: {text}",
                variable=situation_var,
                value=text,
                command=on_select,
                font=("微软雅黑", 10),
                anchor="w",
                bg=bg_color,
                fg=fg_color,
                activebackground="#FFCDD2"
            )
            rb.grid(row=0, column=j, sticky="w", padx=5, pady=2)
    
    # 高级选项统计显示
    stats_frame = tk.LabelFrame(main_frame, text="高级选项统计", font=("微软雅黑", 12, "bold"), bg="white")
    stats_frame.pack(fill="x", pady=10)
    
    stats_text = f"✅ 发现 {high_level_count} 个高级选项（红色背景标记）\n"
    stats_text += f"📊 总计 {all_levels_count} 个选项全部展示\n"
    stats_text += f"🎯 每个境况都包含：低级、中级、高级 三个等级"
    
    stats_label = tk.Label(stats_frame, text=stats_text, font=("微软雅黑", 11), bg="white", justify="left")
    stats_label.pack(padx=10, pady=10)
    
    print("=" * 50)
    print(f"✅ 界面创建完成！高级选项用红色背景标记")
    print(f"📝 请查看界面中的红色背景选项，这些就是'高级'选项")
    
    root.mainloop()

if __name__ == "__main__":
    test_high_level_display()
