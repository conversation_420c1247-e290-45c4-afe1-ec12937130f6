# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

def test_combination_display():
    """测试组合模块的选项显示功能"""
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("组合模块测试")
    root.geometry("800x600")
    
    # 定义主题数据
    themes = [
        "生命力量", "精神力量", "思维力量", 
        "物质资源", "信息资源", "关系资源", "测试项"
    ]
    
    # 生成所有组合
    combinations = []
    for o_theme in themes:
        for p_theme in themes:
            combinations.append(f"对手{o_theme} — 主角{p_theme}")
    
    print(f"生成了 {len(combinations)} 个组合选项:")
    for i, combo in enumerate(combinations, 1):
        print(f"  {i:2d}: {combo}")
    
    # 创建组合模块界面
    combo_frame = ttk.LabelFrame(root, text="组合模块测试", padding=10)
    combo_frame.pack(fill="x", padx=10, pady=10)
    
    # 组合选择器
    ttk.Label(combo_frame, text="选择组合能量:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
    
    combination_var = tk.StringVar()
    combination_combo = ttk.Combobox(
        combo_frame, 
        textvariable=combination_var, 
        values=tuple(combinations),
        state="readonly", 
        width=50
    )
    combination_combo.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
    combo_frame.grid_columnconfigure(1, weight=1)
    
    # 设置默认值
    if combinations:
        combination_var.set(combinations[0])
    
    # 显示当前选择的回调函数
    def on_selection_change(event=None):
        selected = combination_var.get()
        status_label.config(text=f"当前选择: {selected}")
        print(f"用户选择了: {selected}")
    
    combination_combo.bind("<<ComboboxSelected>>", on_selection_change)
    
    # 状态显示
    status_label = ttk.Label(combo_frame, text="请选择一个组合...")
    status_label.grid(row=1, column=0, columnspan=2, pady=10)
    
    # 测试按钮
    def test_all_options():
        """测试所有选项是否可以正确选择"""
        print("\n开始测试所有选项...")
        for i, combo in enumerate(combinations):
            combination_var.set(combo)
            root.update()  # 更新界面
            print(f"测试选项 {i+1}/{len(combinations)}: {combo}")
        print("所有选项测试完成！")
        status_label.config(text="所有选项测试完成！")
    
    test_button = ttk.Button(combo_frame, text="测试所有选项", command=test_all_options)
    test_button.grid(row=2, column=0, columnspan=2, pady=10)
    
    # 显示选项列表
    list_frame = ttk.LabelFrame(root, text="所有可用选项", padding=10)
    list_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 创建列表框显示所有选项
    listbox_frame = tk.Frame(list_frame)
    listbox_frame.pack(fill="both", expand=True)
    
    listbox = tk.Listbox(listbox_frame, font=("微软雅黑", 10))
    scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=listbox.yview)
    
    listbox.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    listbox.config(yscrollcommand=scrollbar.set)
    
    # 填充列表框
    for combo in combinations:
        listbox.insert(tk.END, combo)
    
    # 列表框选择事件
    def on_listbox_select(event):
        selection = listbox.curselection()
        if selection:
            selected_combo = listbox.get(selection[0])
            combination_var.set(selected_combo)
            on_selection_change()
    
    listbox.bind("<<ListboxSelect>>", on_listbox_select)
    
    # 初始化显示
    on_selection_change()
    
    print(f"\n组合模块测试界面已启动")
    print(f"- 组合框包含 {len(combinations)} 个选项")
    print(f"- 列表框显示所有选项")
    print(f"- 可以通过组合框或列表框选择选项")
    
    root.mainloop()

if __name__ == "__main__":
    test_combination_display()
