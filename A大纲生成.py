# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
from tkinter import messagebox
import json
import os
import datetime
import random

class GuanchangCrisisApp(ttk.Frame):
    """
    官场题材专属APP主界面，最终版，智能生成纯剧情流。
    """
    def __init__(self, parent, status_var):
        super().__init__(parent)
        self.theme = "官场"
        self.status_var = status_var
        
        # --- 路径和文件设置 ---
        self.theme_dir = self.theme
        self.manual_save_dir = os.path.join(self.theme_dir, "手动保存")
        self.character_dir = os.path.join(self.theme_dir, "阵营角色")
        self.levels_dir = os.path.join(self.theme_dir, "官场等级")
        self.autosave_file = os.path.join(self.theme_dir, "autosave.json")
        self.levels_file = os.path.join(self.levels_dir, "官场等级.txt")

        os.makedirs(self.manual_save_dir, exist_ok=True)
        os.makedirs(self.character_dir, exist_ok=True)
        os.makedirs(self.levels_dir, exist_ok=True)
        
        # --- 数据结构定义 ---
        self.six_forces = ["生命力量", "思维力量", "精神力量", "物质资源", "信息资源", "关系资源"]
        self.all_forces_data = {}
        self.levels = []
        self.tones = {
            "顺境": ["春风得意", "志得意满", "意气风发"],
            "普通": ["波澜不惊", "按部就班", "若有所思"],
            "逆境": ["忧心忡忡", "如履薄冰", "进退维谷"],
            "绝境": ["剑拔弩张", "四面楚歌", "风声鹤唳"]
        }
        
        # --- 状态变量 ---
        self.selected_force = tk.StringVar()
        self.selected_conflict_var = tk.StringVar()
        self.selected_conflict = None 
        self.selected_action = None 
        self.action_var = tk.StringVar()
        self.result_var = tk.StringVar()
        self.autosave_timer = None
        self.mindset_var = tk.StringVar(value="[ 普通 ]")
        
        # --- 角色模块变量 ---
        self.factions = ["主角阵营", "对手阵营", "上级阵营", "下级阵营", "中立阵营", "群众阵营"]
        self.character_data = {}
        self.character_listboxes = {}
        
        # --- UI组件变量 ---
        self.char1_var = tk.StringVar()
        self.char2_var = tk.StringVar()
        self.conflict_frame = None
        self.action_frames = {}
        self.result_frame = None

        # --- 初始化 ---
        self._create_widgets()
        self._load_all_force_data()
        self._load_levels()
        self._load_character_data()
        self._load_autosaved_content() 

    def _load_all_force_data(self):
        loaded_count = 0
        for force_name in self.six_forces:
            data_file = os.path.join(self.theme_dir, f"{force_name}.json")
            if os.path.exists(data_file):
                try:
                    with open(data_file, 'r', encoding='utf-8') as f:
                        self.all_forces_data[force_name] = json.load(f)
                        loaded_count += 1
                except Exception as e:
                    print(f"Error loading {data_file}: {e}")
        self.status_var.set(f"预加载 {loaded_count}/{len(self.six_forces)} 个主题力量文件")

    def _load_levels(self):
        if os.path.exists(self.levels_file):
            try:
                with open(self.levels_file, 'r', encoding='utf-8') as f:
                    self.levels = [line.strip() for line in f if line.strip()]
            except Exception as e:
                self.status_var.set(f"等级文件加载失败: {e}")

    def on_force_selected(self, force_name):
        self._clear_conflict_and_deeper_selections()
        force_data = self.all_forces_data.get(force_name, {})
        conflict_list = force_data.get("conflict_list", [])
        for conflict in conflict_list:
            crisis_name = conflict.get("crisis_name", "未知冲突")
            rb = tk.Radiobutton(self.conflict_frame, text=crisis_name, variable=self.selected_conflict_var,
                                value=crisis_name, command=self.on_conflict_selected,
                                font=("微软雅黑", 10), anchor='w')
            rb.pack(fill='x', padx=5)
        self.status_var.set(f"已选择“{force_name}”，包含 {len(conflict_list)} 个具体冲突")

    def _load_character_data(self):
        for faction in self.factions:
            filepath = os.path.join(self.character_dir, f"{faction}.txt")
            self.character_data[faction] = []
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    self.character_data[faction] = [line.strip() for line in f if line.strip()]
        self._populate_character_listboxes()

    def _populate_character_listboxes(self):
        for faction, listbox in self.character_listboxes.items():
            listbox.delete(0, tk.END)
            for name in self.character_data.get(faction, []):
                listbox.insert(tk.END, name)

    def perform_autosave(self):
        data_to_save = {
            "content": self.composition_area.get("1.0", tk.END),
            "selected_force_name": self.selected_force.get(),
            "selected_conflict_name": self.selected_conflict_var.get(),
            "selected_action_name": self.action_var.get(),
            "selected_result_key": self.result_var.get(),
            "char_entry1_text": self.char1_var.get(),
            "char_entry2_text": self.char2_var.get(),
        }
        with open(self.autosave_file, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, ensure_ascii=False, indent=4)
        timestamp = datetime.datetime.now(datetime.timezone.utc).strftime('%H:%M:%S')
        self.status_var.set(f"状态已于 {timestamp} 自动保存")

    def _schedule_autosave(self, *args):
        if hasattr(self, 'autosave_timer') and self.autosave_timer:
            self.after_cancel(self.autosave_timer)
        self.autosave_timer = self.after(1500, self.perform_autosave)

    def _load_autosaved_content(self):
        if not os.path.exists(self.autosave_file): return
        try:
            with open(self.autosave_file, 'r', encoding='utf-8') as f: data = json.load(f)
            self.composition_area.insert("1.0", data.get("content", ""))
            self.char1_var.set(data.get("char_entry1_text", ""))
            self.char2_var.set(data.get("char_entry2_text", ""))
            if saved_force := data.get("selected_force_name"):
                self.selected_force.set(saved_force)
                self.on_force_selected(saved_force)
                if saved_conflict := data.get("selected_conflict_name"):
                    self.selected_conflict_var.set(saved_conflict)
                    self.on_conflict_selected()
                    if saved_action := data.get("selected_action_name"):
                        self.action_var.set(saved_action)
                        self.on_action_selected()
                        if saved_result := data.get("selected_result_key"):
                           self.result_var.set(saved_result)
            self.status_var.set("已加载上次保存的状态")
        except Exception as e: self.status_var.set(f"加载状态失败: {e}")
        self.composition_area.edit_modified(False)

    def _create_widgets(self):
        top_pane = tk.PanedWindow(self, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=8)
        top_pane.pack(fill="both", expand=True)

        left_frame_container = tk.Frame(top_pane)
        top_pane.add(left_frame_container, width=420, minsize=380)
        
        left_canvas = tk.Canvas(left_frame_container)
        scrollbar = ttk.Scrollbar(left_frame_container, orient="vertical", command=left_canvas.yview)
        scrollable_frame = ttk.Frame(left_canvas)

        scrollable_frame.bind("<Configure>", lambda e: left_canvas.configure(scrollregion=left_canvas.bbox("all")))
        left_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        left_canvas.configure(yscrollcommand=scrollbar.set)
        
        left_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        generation_frame = tk.LabelFrame(scrollable_frame, text="智能生成大纲", font=("微软雅黑", 11, "bold"))
        generation_frame.pack(fill="x", padx=5, pady=5, anchor='n')
        
        gen_options_frame1 = tk.Frame(generation_frame)
        gen_options_frame1.pack(fill='x', pady=2, padx=5)
        tk.Label(gen_options_frame1, text="主角性格:", font=("微软雅黑", 10)).pack(side='left')
        self.personality_combo = ttk.Combobox(gen_options_frame1, state="readonly", font=("微软雅黑", 10), values=["谨慎型", "平衡型", "激进型"], width=8)
        self.personality_combo.pack(side='left', padx=5)
        self.personality_combo.set("平衡型")
        tk.Label(gen_options_frame1, text="当前心态:", font=("微软雅黑", 10)).pack(side='left')
        tk.Label(gen_options_frame1, textvariable=self.mindset_var, font=("微软雅黑", 10, "bold"), fg="blue").pack(side='left', padx=5)

        gen_options_frame2 = tk.Frame(generation_frame)
        gen_options_frame2.pack(fill='x', pady=2, padx=5)
        tk.Label(gen_options_frame2, text="小层级数:", font=("微软雅黑", 10)).pack(side='left')
        self.tiers_spinbox = ttk.Spinbox(gen_options_frame2, from_=1, to=100, width=5, font=("微软雅黑", 10))
        self.tiers_spinbox.pack(side='left', padx=5)
        self.tiers_spinbox.set(10)
        tk.Label(gen_options_frame2, text="每层章数:", font=("微软雅黑", 10)).pack(side='left')
        self.chapters_spinbox = ttk.Spinbox(gen_options_frame2, from_=1, to=100, width=5, font=("微软雅黑", 10))
        self.chapters_spinbox.pack(side='left', padx=5)
        self.chapters_spinbox.set(10)
        
        tk.Button(generation_frame, text="一键生成大纲", command=self.run_story_engine, bg="#FF8C00", fg="white", font=("微软雅黑", 10, "bold")).pack(pady=5)

        force_frame = tk.LabelFrame(scrollable_frame, text="第一步：选择主题力量", font=("微软雅黑", 11))
        force_frame.pack(fill="x", padx=5, pady=5, anchor='n')
        force_buttons_frame = tk.Frame(force_frame)
        force_buttons_frame.pack(fill="x", pady=5)
        for i, force in enumerate(self.six_forces):
            rb = ttk.Radiobutton(force_buttons_frame, text=force, variable=self.selected_force, value=force, command=lambda f=force: self.on_force_selected(f))
            rb.grid(row=i//3, column=i%3, padx=5, pady=2, sticky='w')
        
        self.conflict_frame = tk.LabelFrame(scrollable_frame, text="第二步：选择具体冲突", font=("微软雅黑", 11))
        self.conflict_frame.pack(fill="x", padx=5, pady=5, anchor='n')

        action_container = tk.LabelFrame(scrollable_frame, text="第三步：选择一个行动", font=("微软雅黑", 11))
        action_container.pack(fill="x", padx=5, pady=5, anchor='n')
        for action_type in ["自己付出", "相互付出", "强制付出", "运气付出"]:
            frame = tk.LabelFrame(action_container, text=action_type, font=("微软雅黑", 10, 'bold'))
            frame.pack(fill="x", padx=5, pady=3, ipady=2)
            self.action_frames[action_type] = frame

        self.result_frame = tk.LabelFrame(scrollable_frame, text="第四步：选择一个结果", font=("微软雅黑", 11))
        self.result_frame.pack(fill="x", padx=5, pady=5, anchor='n')
        
        main_pane, composition_frame = self._create_right_panes(top_pane)
        self._create_character_module(main_pane)

    def _create_right_panes(self, parent):
        main_pane = tk.PanedWindow(parent, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=8)
        parent.add(main_pane)
        
        composition_frame = tk.LabelFrame(main_pane, text="大纲主创作区", font=("微软雅黑", 11))
        main_pane.add(composition_frame, minsize=400)
        
        tools_frame = tk.Frame(composition_frame)
        tools_frame.pack(fill="x", padx=5, pady=5)
        tk.Button(tools_frame, text="清空选择", command=self.clear_inputs, font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=5)
        tk.Button(tools_frame, text="保存创作", command=self.save_composition, bg="#AED581", font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=5)
        tk.Button(tools_frame, text="清空创作区", command=self.clear_composition, font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=5)
        
        comp_scrollbar = tk.Scrollbar(composition_frame)
        comp_scrollbar.pack(side=tk.RIGHT, fill="y")
        self.composition_area = tk.Text(composition_frame, relief="solid", borderwidth=1, wrap="word", font=("微软雅黑", 11), yscrollcommand=comp_scrollbar.set, undo=True)
        self.composition_area.pack(fill="both", expand=True)
        comp_scrollbar.config(command=self.composition_area.yview)
        
        for var in [self.char1_var, self.char2_var, self.selected_force, self.selected_conflict_var, self.action_var, self.result_var]:
            var.trace_add("write", self._schedule_autosave)
        self.composition_area.bind("<<Modified>>", self._schedule_autosave)
        
        return main_pane, composition_frame

    def _create_character_module(self, parent):
        character_module_frame = tk.LabelFrame(parent, text="角色模块", font=("微软雅黑", 11))
        parent.add(character_module_frame, width=480, minsize=400, before=parent.winfo_children()[0])
        entry_frame = tk.Frame(character_module_frame)
        entry_frame.pack(fill='x', pady=5, padx=3)
        tk.Button(entry_frame, text="插入细纲", command=self.generate_outline_manual, bg="#4CAF50", fg="white", font=("微软雅黑", 10, "bold")).pack(side='left', padx=5)
        tk.Label(entry_frame, text="主角:", font=("微软雅黑", 10)).pack(side='left', padx=5)
        tk.Entry(entry_frame, font=("微软雅黑", 10), textvariable=self.char1_var).pack(side='left', fill='x', expand=True, padx=5)
        tk.Label(entry_frame, text="相关人:", font=("微软雅黑", 10)).pack(side='left', padx=5)
        tk.Entry(entry_frame, font=("微软雅黑", 10), textvariable=self.char2_var).pack(side='left', fill='x', expand=True, padx=5)
        char_list_frame = tk.Frame(character_module_frame)
        char_list_frame.pack(fill='both', expand=True, padx=2, pady=2)
        for i, faction in enumerate(self.factions):
            col_frame = tk.LabelFrame(char_list_frame, text=faction, font=("微软雅黑", 10))
            col_frame.grid(row=i//3, column=i%3, sticky="nsew", padx=3, pady=3)
            char_list_frame.grid_rowconfigure(i//3, weight=1)
            char_list_frame.grid_columnconfigure(i%3, weight=1)
            listbox = tk.Listbox(col_frame, font=("微软雅黑", 10), exportselection=False)
            listbox.pack(side=tk.LEFT, fill='both', expand=True)
            self.character_listboxes[faction] = listbox
            scrollbar = tk.Scrollbar(col_frame, orient="vertical", command=listbox.yview)
            scrollbar.pack(side=tk.RIGHT, fill='y')
            listbox.config(yscrollcommand=scrollbar.set)
            listbox.bind("<Double-Button-1>", self.on_character_double_click)

    def _clear_action_widgets(self):
        for frame in self.action_frames.values():
            for widget in frame.winfo_children(): widget.destroy()
        self.selected_action = None; self.action_var.set("")
        self._clear_result_widgets()
        
    def _clear_result_widgets(self):
        if self.result_frame:
            for widget in self.result_frame.winfo_children(): widget.destroy()
        self.result_var.set("")
        
    def _clear_conflict_and_deeper_selections(self):
        if self.conflict_frame:
            for widget in self.conflict_frame.winfo_children(): widget.destroy()
        self.selected_conflict_var.set(''); self.selected_conflict = None
        self._clear_action_widgets()

    def on_conflict_selected(self, event=None):
        self._clear_action_widgets()
        selected_crisis_name = self.selected_conflict_var.get()
        if not self.selected_force.get(): return
        force_data = self.all_forces_data.get(self.selected_force.get(), {})
        conflict_list = force_data.get("conflict_list", [])
        self.selected_conflict = next((c for c in conflict_list if c['crisis_name'] == selected_crisis_name), None)
        if not self.selected_conflict: return
        for action in self.selected_conflict.get("actions", []):
            action_type = action.get("action_type")
            if parent_frame := self.action_frames.get(action_type):
                rb = tk.Radiobutton(parent_frame, text=action['action_name'], variable=self.action_var, value=action['action_name'], command=self.on_action_selected, font=("微软雅黑", 10), anchor='w')
                rb.pack(fill='x', padx=5)
        self._schedule_autosave()

    def on_action_selected(self):
        self._clear_result_widgets()
        selected_action_name = self.action_var.get()
        if not self.selected_conflict: return
        self.selected_action = next((a for a in self.selected_conflict['actions'] if a['action_name'] == selected_action_name), None)
        if not self.selected_action: return
        
        results = self.selected_action.get("results", {})
        for result_key, result_data in results.items():
            rb = tk.Radiobutton(self.result_frame, text=result_data['final_result'], variable=self.result_var, value=result_key, font=("微软雅黑", 10), anchor='w', command=self._schedule_autosave)
            rb.pack(fill='x', padx=5)
        self._schedule_autosave()

    def on_character_double_click(self, event):
        listbox = event.widget
        if not listbox.curselection(): return
        selected_name = listbox.get(listbox.curselection()[0])
        if not self.char1_var.get(): self.char1_var.set(selected_name)
        elif not self.char2_var.get(): self.char2_var.set(selected_name)
        else: self.composition_area.insert(tk.INSERT, f"{selected_name}、")
            
    def generate_outline_manual(self):
        if not all([self.selected_conflict, self.selected_action, self.result_var.get()]):
            messagebox.showwarning("提示", "请完整选择“冲突”、“行动”和“结果”。")
            return
        
        char1 = self.char1_var.get() or "主角"
        char2 = self.char2_var.get()
        
        outline = self.format_outline_string(char1, char2, self.selected_conflict, self.selected_action, self.result_var.get())
        
        self.composition_area.insert(tk.INSERT, outline)
        result_text = self.selected_action['results'][self.result_var.get()]['final_result']
        self.status_var.set(f"已插入：{self.selected_action['action_name']} -> {result_text}")
        self.char1_var.set(""); self.char2_var.set("")

    def format_outline_string(self, char1, char2, conflict, action, result_key, tone=""):
        crisis_text = conflict['crisis_name']
        opponent_action = conflict.get('opponent_action', '未知行动')
        action_text = action['action_name']
        action_type = action['action_type']
        
        result_data = action.get("results", {}).get(result_key, {})
        opponent_counter = result_data.get('opponent_counter', '未知应对')
        final_result = result_data.get('final_result', '未知结果')

        tone_str = f"[基调：{tone}] " if tone else ""
        char1_str = f"({char1 or '主角'})"
        char2_str = f"({char2 or '对手'})"

        if action_type == "自己付出":
            response_part = f"{char1_str}决定采取【{action_text}】进行应对，"
        elif action_type == "相互付出":
            response_part = f"{char1_str}选择寻求与{char2_str}进行【{action_text}】，"
        elif action_type == "强制付出":
            response_part = f"{char1_str}决定对{char2_str}采取【{action_text}】，"
        elif action_type == "运气付出":
            response_part = f"{char1_str}寄希望于【{action_text}】，"
        else:
            response_part = f"{char1_str}以【{action_text}】反制，"
            
        return f"{tone_str}因{char2_str}采取【{opponent_action}】，{char1_str}面临【{crisis_text}】，{response_part}对手则以【{opponent_counter}】应对，最终【{final_result}】。\n"

    def run_story_engine(self):
        if not self.levels:
            messagebox.showerror("错误", "未找到`官场等级.txt`文件，无法生成大纲。")
            return
        if not self.all_forces_data:
            messagebox.showerror("错误", "未加载任何力量JSON文件，无法生成大纲。")
            return
        try:
            tiers_per_level = int(self.tiers_spinbox.get())
            chapters_per_tier = int(self.chapters_spinbox.get())
        except (ValueError, tk.TclError):
            messagebox.showerror("错误", "小层级数和每层章数必须是有效的数字。")
            return
            
        protagonist = {
            "name": "主角", "level_index": 0, "mindset": "普通",
            "personality": self.personality_combo.get(), 
            "consecutive_failures": 0, "last_result_key": None
        }
        
        full_outline_text = ""
        total_chapters = len(self.levels) * tiers_per_level * chapters_per_tier
        
        for i in range(1, total_chapters + 1):
            force_name = random.choice(self.six_forces)
            if not self.all_forces_data.get(force_name, {}).get('conflict_list'): continue

            conflict = random.choice(self.all_forces_data[force_name]['conflict_list'])
            action = self._engine_select_action(protagonist, conflict['actions'])
            result_key = self._engine_select_result(protagonist, action['results'])

            self.mindset_var.set(f"[ {protagonist['mindset']} ]")
            tone = random.choice(self.tones.get(protagonist['mindset'], ["平常心态"]))
            
            # --- 修改：只生成核心剧情行 ---
            outline = self.format_outline_string(protagonist['name'], "对手", conflict, action, result_key, tone)
            full_outline_text += outline

            # 更新主角状态 (后台逻辑)
            protagonist['last_result_key'] = result_key
            if result_key in ["failure", "cost"]:
                protagonist["consecutive_failures"] += 1
                if protagonist["mindset"] == "顺境": protagonist["mindset"] = "普通"
                elif protagonist["mindset"] == "普通": protagonist["mindset"] = "逆境"
                else: protagonist["mindset"] = "绝境"
            else: 
                protagonist["consecutive_failures"] = 0
                if protagonist["mindset"] == "绝境": protagonist["mindset"] = "逆境"
                elif protagonist["mindset"] == "逆境": protagonist["mindset"] = "普通"
                else: protagonist["mindset"] = "顺境"

            # 晋升逻辑 (后台逻辑)
            if i > 0 and i % (tiers_per_level * chapters_per_tier) == 0:
                if protagonist["level_index"] < len(self.levels) - 1:
                    protagonist["level_index"] += 1
                    protagonist["mindset"] = "顺境"
        
        self.composition_area.delete('1.0', tk.END)
        self.composition_area.insert('1.0', full_outline_text)
        self.status_var.set(f"已生成 {total_chapters} 行纯剧情流大纲！")

    def _engine_select_action(self, protagonist, actions):
        p = protagonist["personality"]
        m = protagonist["mindset"]
        # 更新为四种付出类型的概率分布 [自己付出, 相互付出, 强制付出, 运气付出]
        probs = {"谨慎型": [0.6, 0.2, 0.1, 0.1], "平衡型": [0.25, 0.35, 0.25, 0.15], "激进型": [0.1, 0.2, 0.6, 0.1]}

        if m == "顺境":
            if p == "谨慎型": probs[p] = [0.8, 0.1, 0.05, 0.05]
            if p == "平衡型": probs[p] = [0.4, 0.4, 0.15, 0.05]
            if p == "激进型": probs[p] = [0.2, 0.3, 0.4, 0.1]
        elif m == "逆境":
            if p == "谨慎型": probs[p] = [0.3, 0.4, 0.1, 0.2]
            if p == "平衡型": probs[p] = [0.2, 0.3, 0.3, 0.2]
            if p == "激进型": probs[p] = [0.1, 0.2, 0.5, 0.2]
        elif m == "绝境":
            # 绝境时更多依赖运气和强制手段
            if p == "谨慎型": probs[p] = [0.1, 0.2, 0.2, 0.5]
            if p == "平衡型": probs[p] = [0.1, 0.2, 0.4, 0.3]
            if p == "激进型": probs[p] = [0.05, 0.15, 0.6, 0.2]

        action_type = random.choices(["自己付出", "相互付出", "强制付出", "运气付出"], weights=probs[p], k=1)[0]
        available_actions = [a for a in actions if a['action_type'] == action_type]
        return random.choice(available_actions) if available_actions else random.choice(actions)

    def _engine_select_result(self, protagonist, results):
        m = protagonist["mindset"]
        f = protagonist["consecutive_failures"]
        lrk = protagonist["last_result_key"]
        
        weights = {"success": 0.25, "failure": 0.25, "cost": 0.25, "unexpected": 0.25}
        
        if lrk == 'cost':
            weights = {"success": 0.15, "failure": 0.35, "cost": 0.40, "unexpected": 0.10}
        elif lrk == 'unexpected':
            weights = {"success": 0.35, "failure": 0.15, "cost": 0.10, "unexpected": 0.40}
        
        if m == "顺境": weights = {"success": 0.5, "failure": 0.1, "cost": 0.2, "unexpected": 0.2}
        elif m == "逆境": weights = {"success": 0.2, "failure": 0.4, "cost": 0.3, "unexpected": 0.1}

        if f >= 3 and m == "绝境": 
            weights = {"success": 0.4, "failure": 0.1, "cost": 0.1, "unexpected": 0.4}

        result_key = random.choices(list(results.keys()), weights=list(weights.values()), k=1)[0]
        return result_key

    def clear_inputs(self):
        self.selected_force.set('')
        self._clear_conflict_and_deeper_selections()
        self.status_var.set("已清空所有选择")

    def clear_composition(self):
        if messagebox.askyesno("确认", "确定要清空主创作区吗？此操作无法撤销。"):
            self.composition_area.delete('1.0', tk.END)
            
    def save_composition(self):
        content = self.composition_area.get("1.0", tk.END).strip()
        if not content:
            self.status_var.set("创作区为空"); return
        now = datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d_%H-%M-%S")
        filepath = os.path.join(self.manual_save_dir, f"官场大纲_{now}.txt")
        with open(filepath, 'w', encoding='utf-8') as f: f.write(content)
        self.status_var.set(f"内容已手动保存到: {os.path.basename(filepath)}")

    def on_closing(self):
        self.perform_autosave()

class MainApplication:
    def __init__(self, root):
        self.root = root
        self.root.title("小说大纲生成器 (最终版)")
        self.root.geometry("1800x950")
        
        self.status_var = tk.StringVar(value="欢迎使用最终版！请先选择主题力量，或直接使用智能生成功能。")
        
        self.app_frame = GuanchangCrisisApp(self.root, self.status_var)
        self.app_frame.pack(expand=True, fill='both', padx=10, pady=10)
            
        status_bar = tk.Label(self.root, textvariable=self.status_var, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def on_closing(self):
        self.app_frame.on_closing()
        self.root.destroy()

if __name__ == "__main__":
    theme_dir = "官场"
    os.makedirs(theme_dir, exist_ok=True)
    os.makedirs(os.path.join(theme_dir, "手动保存"), exist_ok=True)
    os.makedirs(os.path.join(theme_dir, "阵营角色"), exist_ok=True)
    os.makedirs(os.path.join(theme_dir, "官场等级"), exist_ok=True)
    root = tk.Tk()
    app = MainApplication(root)
    root.mainloop()