# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

def test_full_display():
    """测试全展示组合选项界面"""
    
    root = tk.Tk()
    root.title("组合选项全展示测试")
    root.geometry("1200x800")
    
    # 定义主题数据
    themes = [
        "生命力量", "精神力量", "思维力量", 
        "物质资源", "信息资源", "关系资源", "测试项"
    ]
    
    # 生成所有组合
    combinations = []
    for o_theme in themes:
        for p_theme in themes:
            combinations.append(f"对手{o_theme} — 主角{p_theme}")
    
    print(f"生成了 {len(combinations)} 个组合选项，将全部展示")
    
    # 主框架
    main_frame = ttk.Frame(root, padding=10)
    main_frame.pack(fill="both", expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="组合模块 - 全部49个选项展示", font=("微软雅黑", 14, "bold"))
    title_label.pack(pady=10)
    
    # 组合模块框架
    combo_module = ttk.LabelFrame(main_frame, text="第一步: 选择组合能量", padding=10)
    combo_module.pack(fill="both", expand=True, padx=5, pady=5)
    
    # 选择变量
    combination_var = tk.StringVar()
    
    # 当前选择显示
    current_frame = ttk.Frame(combo_module)
    current_frame.pack(fill="x", pady=5)
    
    ttk.Label(current_frame, text="当前选择:", font=("微软雅黑", 10, "bold")).pack(side="left")
    current_label = ttk.Label(current_frame, text="", font=("微软雅黑", 10), foreground="blue")
    current_label.pack(side="left", padx=10)
    
    def on_selection_change():
        selected = combination_var.get()
        current_label.config(text=selected)
        try:
            index = combinations.index(selected) + 1
            print(f"选择了第 {index} 个选项: {selected}")
        except ValueError:
            print(f"选择了: {selected}")
    
    # 创建滚动框架
    canvas = tk.Canvas(combo_module, bg="white")
    scrollbar = ttk.Scrollbar(combo_module, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # 创建7x7网格显示所有49个选项
    print("创建选项网格...")
    for i, combination in enumerate(combinations):
        row = i // 7
        col = i % 7
        
        rb = tk.Radiobutton(
            scrollable_frame, 
            text=combination, 
            variable=combination_var,
            value=combination, 
            command=on_selection_change,
            font=("微软雅黑", 9),
            anchor='w',
            wraplength=150,
            justify='left',
            bg="white"
        )
        rb.grid(row=row, column=col, sticky="nw", padx=3, pady=2, ipadx=5, ipady=2)
        
        # 为每列设置最小宽度
        scrollable_frame.grid_columnconfigure(col, minsize=160)
    
    # 放置滚动组件
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # 设置默认选择
    if combinations:
        combination_var.set(combinations[0])
        on_selection_change()
    
    # 统计信息
    info_frame = ttk.Frame(main_frame)
    info_frame.pack(fill="x", pady=5)
    
    info_text = f"共展示 {len(combinations)} 个组合选项 (7个主题 × 7个主题 = 49个选项)"
    ttk.Label(info_frame, text=info_text, font=("微软雅黑", 10), foreground="green").pack()
    
    # 鼠标滚轮支持
    def on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    canvas.bind("<MouseWheel>", on_mousewheel)
    
    print("界面创建完成！")
    print("- 所有49个选项都显示为单选按钮")
    print("- 按7x7网格排列")
    print("- 支持鼠标滚轮滚动")
    print("- 点击任意选项即可选择")
    
    root.mainloop()

if __name__ == "__main__":
    test_full_display()
