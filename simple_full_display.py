# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

def create_full_display():
    """创建全展示组合选项界面"""
    
    root = tk.Tk()
    root.title("组合选项全展示")
    root.geometry("1000x700")
    
    # 主题数据
    themes = ["生命力量", "精神力量", "思维力量", "物质资源", "信息资源", "关系资源", "测试项"]
    
    # 生成所有组合
    combinations = []
    for o_theme in themes:
        for p_theme in themes:
            combinations.append(f"对手{o_theme} — 主角{p_theme}")
    
    print(f"创建全展示界面，包含 {len(combinations)} 个选项")
    
    # 主框架
    main_frame = tk.Frame(root)
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(main_frame, text="组合模块 - 所有49个选项", font=("微软雅黑", 14, "bold"))
    title_label.pack(pady=10)
    
    # 选择变量
    selection_var = tk.StringVar()
    
    # 当前选择显示
    current_frame = tk.Frame(main_frame)
    current_frame.pack(fill="x", pady=5)
    
    tk.Label(current_frame, text="当前选择:", font=("微软雅黑", 11, "bold")).pack(side="left")
    current_label = tk.Label(current_frame, text="", font=("微软雅黑", 10), fg="blue")
    current_label.pack(side="left", padx=10)
    
    def on_select():
        selected = selection_var.get()
        current_label.config(text=selected)
        print(f"选择了: {selected}")
    
    # 选项展示区域
    options_frame = tk.LabelFrame(main_frame, text="选择组合能量 (点击任意选项)", font=("微软雅黑", 11))
    options_frame.pack(fill="both", expand=True, pady=10)
    
    # 创建滚动区域
    canvas = tk.Canvas(options_frame)
    scrollbar = tk.Scrollbar(options_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas)
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # 创建所有选项的单选按钮
    print("创建单选按钮...")
    for i, combination in enumerate(combinations):
        row = i // 7  # 每行7个
        col = i % 7
        
        rb = tk.Radiobutton(
            scrollable_frame,
            text=combination,
            variable=selection_var,
            value=combination,
            command=on_select,
            font=("微软雅黑", 9),
            anchor="w",
            justify="left"
        )
        rb.grid(row=row, column=col, sticky="w", padx=5, pady=3)
    
    # 布局滚动组件
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # 鼠标滚轮支持
    def on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    canvas.bind("<MouseWheel>", on_mousewheel)
    
    # 设置默认选择
    if combinations:
        selection_var.set(combinations[0])
        on_select()
    
    # 底部信息
    info_label = tk.Label(main_frame, text=f"共 {len(combinations)} 个选项全部展示", 
                         font=("微软雅黑", 10), fg="green")
    info_label.pack(pady=5)
    
    print("界面创建完成，启动程序...")
    root.mainloop()

if __name__ == "__main__":
    create_full_display()
