# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

def test_work_tactics():
    """测试工作主题的对策和反制"""
    
    root = tk.Tk()
    root.title("工作主题对策反制测试")
    root.geometry("1400x800")
    
    print("🔍 测试工作主题的对策和反制")
    print("=" * 60)
    
    # 工作主题专用对策和反制
    work_tactics = {
        "对策": [
            "庆祝升职：升职后举办庆祝活动，巩固地位",
            "拉拢同事：主动与同事建立良好关系",
            "展示能力：在重要场合展现工作能力",
            "寻求机会：主动寻找更好的工作机会",
            "承认错误：主动承认工作失误并改正",
            "化解冲突：积极化解与同事的矛盾",
            "减压调节：合理安排工作，减轻压力",
            "危机应对：制定应对行业危机的策略"
        ],
        "反制": [
            "使绊子：对升职的同事进行暗中阻挠",
            "散布谣言：传播不利于对手的消息",
            "抢夺功劳：将他人的工作成果据为己有",
            "孤立对手：联合他人排斥特定同事",
            "揭发错误：故意放大他人的工作失误",
            "挑拨离间：在同事间制造矛盾冲突",
            "施加压力：给对手安排过重的工作",
            "趁火打劫：利用危机时机打击对手"
        ]
    }
    
    # 结果类型及描述
    result_descriptions = {
        "成功": "达成预期目标，获得理想结果",
        "失败": "未能达成目标，计划受挫",
        "代价": "达成目标但付出了额外代价",
        "意外": "出现意想不到的结果或转折"
    }
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🎯 工作主题对策反制测试", 
        font=("微软雅黑", 18, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=10)
    
    # 创建水平分割
    pane = tk.PanedWindow(main_frame, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=8)
    pane.pack(fill="both", expand=True)
    
    def create_tactics_display(parent, title, tactics_list, color):
        """创建对策或反制显示区域"""
        frame = tk.LabelFrame(parent, text=title, font=("微软雅黑", 14, "bold"), bg="white", fg=color)
        pane.add(frame, minsize=650)
        
        # 选择变量
        selected_var = tk.StringVar()
        
        # 显示选项
        for i, tactic in enumerate(tactics_list):
            rb = tk.Radiobutton(
                frame,
                text=tactic,
                variable=selected_var,
                value=tactic,
                font=("微软雅黑", 11),
                anchor="w",
                wraplength=600,
                justify="left",
                bg="white",
                activebackground="#E3F2FD"
            )
            rb.pack(anchor="w", padx=10, pady=5)
        
        # 设置默认选择
        if tactics_list:
            selected_var.set(tactics_list[0])
        
        # 当前选择显示
        current_frame = tk.Frame(frame, bg="white")
        current_frame.pack(fill="x", padx=10, pady=10)
        
        tk.Label(current_frame, text="当前选择:", font=("微软雅黑", 12, "bold"), bg="white").pack(anchor="w")
        current_label = tk.Label(
            current_frame, 
            text="", 
            font=("微软雅黑", 11), 
            fg=color, 
            bg="white",
            wraplength=600,
            justify="left"
        )
        current_label.pack(anchor="w", padx=10)
        
        def on_select():
            selected = selected_var.get()
            current_label.config(text=selected)
            print(f"{title} 选择: {selected}")
        
        selected_var.trace('w', lambda *args: on_select())
        
        return selected_var
    
    # 创建对策和反制显示区域
    tactics_var = create_tactics_display(pane, "🎯 工作对策 (8个选项)", work_tactics["对策"], "#27AE60")
    counters_var = create_tactics_display(pane, "⚔️ 工作反制 (8个选项)", work_tactics["反制"], "#E74C3C")
    
    # 底部结果显示区域
    result_frame = tk.LabelFrame(main_frame, text="📊 结果类型说明", font=("微软雅黑", 14, "bold"), bg="white")
    result_frame.pack(fill="x", pady=10)
    
    for result_type, description in result_descriptions.items():
        result_item_frame = tk.Frame(result_frame, bg="white")
        result_item_frame.pack(fill="x", padx=10, pady=3)
        
        # 结果类型标签
        type_color = {"成功": "#27AE60", "失败": "#E74C3C", "代价": "#F39C12", "意外": "#9B59B6"}
        type_label = tk.Label(
            result_item_frame,
            text=f"【{result_type}】",
            font=("微软雅黑", 11, "bold"),
            bg="white",
            fg=type_color.get(result_type, "#333333")
        )
        type_label.pack(side="left", padx=(0, 10))
        
        # 描述标签
        desc_label = tk.Label(
            result_item_frame,
            text=description,
            font=("微软雅黑", 11),
            bg="white",
            fg="#333333"
        )
        desc_label.pack(side="left")
    
    # 测试按钮
    test_frame = tk.Frame(main_frame, bg="white")
    test_frame.pack(fill="x", pady=10)
    
    def test_combination():
        """测试对策反制组合"""
        selected_tactic = tactics_var.get()
        selected_counter = counters_var.get()
        
        print("\n" + "="*60)
        print("🎯 测试组合:")
        print(f"对策: {selected_tactic}")
        print(f"反制: {selected_counter}")
        print("="*60)
        
        # 模拟结果生成
        import random
        result_type = random.choice(list(result_descriptions.keys()))
        result_desc = result_descriptions[result_type]
        
        result_text = f"【{result_type}】{result_desc}"
        
        # 显示结果
        result_window = tk.Toplevel(root)
        result_window.title("测试结果")
        result_window.geometry("600x400")
        result_window.configure(bg="white")
        
        tk.Label(
            result_window,
            text="🎊 测试结果",
            font=("微软雅黑", 16, "bold"),
            bg="white",
            fg="#2E86AB"
        ).pack(pady=20)
        
        tk.Label(
            result_window,
            text=f"对策: {selected_tactic}",
            font=("微软雅黑", 12),
            bg="white",
            wraplength=550,
            justify="left"
        ).pack(anchor="w", padx=20, pady=5)
        
        tk.Label(
            result_window,
            text=f"反制: {selected_counter}",
            font=("微软雅黑", 12),
            bg="white",
            wraplength=550,
            justify="left"
        ).pack(anchor="w", padx=20, pady=5)
        
        tk.Label(
            result_window,
            text=f"结果: {result_text}",
            font=("微软雅黑", 12, "bold"),
            bg="white",
            fg="#E74C3C",
            wraplength=550,
            justify="left"
        ).pack(anchor="w", padx=20, pady=15)
    
    test_button = tk.Button(
        test_frame,
        text="🎯 测试对策反制组合",
        command=test_combination,
        font=("微软雅黑", 12, "bold"),
        bg="#3498DB",
        fg="white",
        padx=20,
        pady=10
    )
    test_button.pack()
    
    # 控制台输出
    print("\n📋 工作主题对策反制详情:")
    print("-" * 60)
    
    print("\n🎯 对策选项 (8个):")
    for i, tactic in enumerate(work_tactics["对策"], 1):
        print(f"  {i:2d}. {tactic}")
    
    print("\n⚔️ 反制选项 (8个):")
    for i, counter in enumerate(work_tactics["反制"], 1):
        print(f"  {i:2d}. {counter}")
    
    print("\n📊 结果类型 (4种):")
    for result_type, description in result_descriptions.items():
        print(f"  【{result_type}】{description}")
    
    print("\n" + "=" * 60)
    print("✅ 工作主题对策反制测试界面创建完成")
    print("📝 可以选择不同的对策和反制进行测试")
    
    root.mainloop()

if __name__ == "__main__":
    test_work_tactics()
