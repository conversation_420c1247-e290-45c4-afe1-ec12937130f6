# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
from tkinter import messagebox
import json
import os

class PlottingSimulatorApp(tk.Frame):
    def __init__(self, parent, status_var, rules_data):
        super().__init__(parent)
        self.status_var = status_var
        
        # --- Data Structures ---
        self.rules = rules_data
        self.MASTER_ENERGY_FOLDER = "组合能量"
        # 固定的6个能量文件夹
        self.themes = ["生命力量", "精神力量", "思维力量", "物质资源", "信息资源", "关系资源"]
        
        # 通用对策类型
        self.action_types = [
            "自主-(好)", "互动-(好)", "强制-(好)", "运气-(好)",
            "自主-(坏)", "互动-(坏)", "强制-(坏)", "运气-(坏)"
        ]

        # 主题专用手段字典 (从JSON文件动态加载)
        self.theme_tactics = {}

        # 结果类型及描述
        self.result_descriptions = {
            "成功": "达成预期目标，获得理想结果",
            "失败": "未能达成目标，计划受挫",
            "代价": "达成目标但付出了额外代价",
            "意外": "出现意想不到的结果或转折"
        }
        
        self.strategic_stances = [
            "对手 机遇 / 主角 机遇 (强强对抗)", "对手 危机 / 主角 机遇 (主角优势)",
            "对手 机遇 / 主角 危机 (主角劣势)", "对手 危机 / 主角 危机 (泥潭互搏)"
        ]

        self.tactical_stances = [
            "主角(对策) -> 对手(对策) -> 主角(反制) -> 对手(反制)", "主角(对策) -> 对手(反制) -> 主角(反制) -> 对手(对策)",
            "主角(对策) -> 对手(反制) -> 对手(对策) -> 主角(反制)", "主角(反制) -> 对手(反制) -> 主角(对策) -> 对手(对策)",
            "主角(反制) -> 对手(对策) -> 主角(对策) -> 对手(反制)", "主角(反制) -> 对手(对策) -> 对手(反制) -> 主角(对策)",
            "对手(对策) -> 主角(对策) -> 对手(反制) -> 主角(反制)", "对手(对策) -> 主角(反制) -> 对手(反制) -> 主角(对策)",
            "对手(对策) -> 主角(反制) -> 主角(对策) -> 对手(反制)", "对手(反制) -> 主角(反制) -> 对手(对策) -> 主角(对策)",
            "对手(反制) -> 主角(对策) -> 对手(对策) -> 主角(反制)", "对手(反制) -> 主角(对策) -> 主角(反制) -> 对手(对策)",
        ]
        
        self.plot_models = ["格式一：完整博弈", "格式四：行动-反应"]

        # --- UI Variables ---
        self.plot_model_var = tk.StringVar()
        self.combination_var = tk.StringVar()
        self.strategic_var = tk.StringVar()
        self.tactical_var = tk.StringVar()
        
        self.protagonist_energy_var, self.protagonist_theme_var = tk.StringVar(), tk.StringVar()
        self.protagonist_situation, self.protagonist_tactic, self.protagonist_counter = tk.StringVar(), tk.StringVar(), tk.StringVar()
        
        self.opponent_energy_var, self.opponent_theme_var = tk.StringVar(), tk.StringVar()
        self.opponent_situation, self.opponent_tactic, self.opponent_counter = tk.StringVar(), tk.StringVar(), tk.StringVar()
        
        self.result_var = tk.StringVar(value="[ 点击'生成大纲' ]")

        self._is_updating = False
        
        # --- References to UI Frames and Widgets ---
        self.p_theme_frame, self.o_theme_frame = None, None
        self.p_sit_frame, self.o_sit_frame = None, None
        self.p_tactic_frame, self.o_tactic_frame = None, None
        self.p_counter_frame, self.o_counter_frame = None, None
        self.tactical_frame = None
        
        self._create_widgets()
        self._set_initial_values_and_traces()

    def _set_initial_values_and_traces(self):
        if self.themes:
            self.combination_var.set(f"对手{self.themes[0]} — 主角{self.themes[0]}")
        
        if self.strategic_stances: self.strategic_var.set(self.strategic_stances[0])
        if self.tactical_stances: self.tactical_var.set(self.tactical_stances[0])
        if self.plot_models: self.plot_model_var.set(self.plot_models[0])

        self.combination_var.trace_add('write', self._synchronize_energy)
        self.protagonist_energy_var.trace_add('write', lambda *args: self._update_themes('protagonist'))
        self.opponent_energy_var.trace_add('write', lambda *args: self._update_themes('opponent'))
        self.protagonist_theme_var.trace_add('write', lambda *args: self._load_and_populate_situations('protagonist'))
        self.opponent_theme_var.trace_add('write', lambda *args: self._load_and_populate_situations('opponent'))
        self.strategic_var.trace_add('write', self._strategic_stance_changed)
        self.plot_model_var.trace_add('write', self._update_ui_for_plot_model)
        
        self._synchronize_energy()
        self._update_ui_for_plot_model()

    # --- REWRITTEN: Robust recursive function to toggle widget states ---
    def _toggle_widget_state(self, parent_widget, state):
        """Recursively sets the state for a widget and all its children."""
        try:
            parent_widget.configure(state=state)
        except tk.TclError:
            # This widget (like a basic Frame) might not have a 'state' option.
            pass

        for child in parent_widget.winfo_children():
            self._toggle_widget_state(child, state)

    def _update_ui_for_plot_model(self, *args):
        """Enables/Disables UI sections based on the selected plot model."""
        model = self.plot_model_var.get()

        all_frames = [
            self.p_sit_frame, self.p_tactic_frame, self.p_counter_frame,
            self.o_sit_frame, self.o_tactic_frame, self.o_counter_frame,
            self.tactical_frame
        ]
        
        # First, enable everything
        for frame in all_frames:
            if frame:
                self._toggle_widget_state(frame, tk.NORMAL)

        # Then, disable parts based on the model
        if "格式四" in model:
            if self.p_counter_frame: self._toggle_widget_state(self.p_counter_frame, tk.DISABLED)
            if self.o_sit_frame: self._toggle_widget_state(self.o_sit_frame, tk.DISABLED)
            if self.o_tactic_frame: self._toggle_widget_state(self.o_tactic_frame, tk.DISABLED)
            if self.tactical_frame: self._toggle_widget_state(self.tactical_frame, tk.DISABLED)
        
        self.protagonist_situation.set(""); self.opponent_situation.set("")
        self.protagonist_tactic.set(""); self.protagonist_counter.set("")
        self.opponent_tactic.set(""); self.opponent_counter.set("")

    def _synchronize_energy(self, *args):
        if self._is_updating: return
        self._is_updating = True
        try:
            combo_selection = self.combination_var.get()
            if "—" in combo_selection:
                o_energy, p_energy = [s.strip().replace("对手", "").replace("主角", "") for s in combo_selection.split("—")]
                if self.opponent_energy_var.get() != o_energy: self.opponent_energy_var.set(o_energy)
                if self.protagonist_energy_var.get() != p_energy: self.protagonist_energy_var.set(p_energy)
        finally:
            self._is_updating = False
    
    def _strategic_stance_changed(self, *args):
        self._load_and_populate_situations('protagonist')
        self._load_and_populate_situations('opponent')

    def _update_themes(self, who):
        energy_folder, theme_frame, theme_var = (
            (self.protagonist_energy_var.get(), self.p_theme_frame, self.protagonist_theme_var) if who == 'protagonist' 
            else (self.opponent_energy_var.get(), self.o_theme_frame, self.opponent_theme_var)
        )
        for widget in theme_frame.winfo_children(): widget.destroy()
        theme_var.set("")
        if not energy_folder: self._load_and_populate_situations(who); return
        full_path = os.path.join(self.MASTER_ENERGY_FOLDER, energy_folder)
        if not os.path.isdir(full_path): self._load_and_populate_situations(who); return
        try:
            themes = sorted([f.replace('.json', '').replace('.jsoN', '') for f in os.listdir(full_path) if f.endswith('.json') or f.endswith('.jsoN')])
            if themes:
                # 创建网格布局显示所有主题
                grid_frame = ttk.Frame(theme_frame)
                grid_frame.pack(fill="x", padx=5, pady=5)

                for i, theme in enumerate(themes):
                    row = i // 5  # 每行5个
                    col = i % 5

                    rb = ttk.Radiobutton(
                        grid_frame,
                        text=theme,
                        variable=theme_var,
                        value=theme,
                        command=lambda t=theme, w=who: [self._load_and_populate_situations(w), self._update_tactics(w, t)]
                    )
                    rb.grid(row=row, column=col, sticky="w", padx=5, pady=2)

                theme_var.set(themes[0])
                # 初始化时也要调用_update_tactics
                self._update_tactics(who, themes[0])
            else:
                ttk.Label(theme_frame, text="此文件夹下没有主题 (.json)", foreground="orange").pack()
                self._load_and_populate_situations(who)
        except Exception as e:
            ttk.Label(theme_frame, text=f"读取文件夹失败: {e}", foreground="red").pack()
            self._load_and_populate_situations(who)

    def _load_and_populate_situations(self, who):
        frame, variable, energy_folder, theme_file, strategic_selection = (
            (self.p_sit_frame, self.protagonist_situation, self.protagonist_energy_var.get(), self.protagonist_theme_var.get(), self.strategic_var.get()) if who == 'protagonist'
            else (self.o_sit_frame, self.opponent_situation, self.opponent_energy_var.get(), self.opponent_theme_var.get(), self.strategic_var.get())
        )
        if not frame: return
        for widget in frame.winfo_children(): widget.destroy()
        variable.set("")
        if not energy_folder or not theme_file: return
        situation_type = ("机遇" if "主角 机遇" in strategic_selection else "危机") if who == 'protagonist' else ("机遇" if "对手 机遇" in strategic_selection else "危机")
        filename = os.path.join(self.MASTER_ENERGY_FOLDER, energy_folder, f"{theme_file}.json")
        try:
            with open(filename, 'r', encoding='utf-8') as f: data = json.load(f)
            situation_list = data.get(situation_type, [])
            ttk.Label(frame, text=f"{situation_type}:", font=("微软雅黑", 11, "bold")).pack(anchor="w", padx=5, pady=5)
            for theme_obj in situation_list:
                main_theme_name = theme_obj.get("theme_name", "未命名主题")
                main_theme_frame = ttk.LabelFrame(frame, text=main_theme_name, padding=5)
                main_theme_frame.pack(fill="x", padx=2, pady=(8, 2))
                categories = ["自主", "互动", "强制", "运气"]
                for category_name in categories:
                    category_data = theme_obj.get(category_name)
                    if not category_data: continue
                    sub_frame_text = category_data.get("name", f"{category_name}型")
                    sub_frame = ttk.LabelFrame(main_theme_frame, text=sub_frame_text, padding=3); sub_frame.pack(fill='x', padx=2, pady=2)
                    levels_frame = ttk.Frame(sub_frame); levels_frame.pack(fill="x", anchor="w")
                    for j, (level, text) in enumerate(list(category_data.get("levels", {}).items())):
                        rb = tk.Radiobutton(levels_frame, text=f"{level}: {text}", variable=variable, value=text, font=("微软雅黑", 9), anchor="w", wraplength=120, justify="left", bg=("#FFE5E5" if "高" in level else "SystemButtonFace"))
                        rb.grid(row=0, column=j, sticky="w", padx=3, pady=2)
            self.status_var.set(f"已加载 {os.path.basename(filename)}")
        except FileNotFoundError:
             ttk.Label(frame, text=f"文件未找到: {filename}", foreground="red").pack(anchor="w")
        except Exception as e:
            ttk.Label(frame, text=f"加载失败: {filename}\n错误: {e}", foreground="red").pack(anchor="w")

    def _update_tactics(self, who, theme_name):
        """根据选择的主题更新手段选项"""
        tactic_frame = self.p_tactic_frame if who == 'protagonist' else self.o_tactic_frame
        counter_frame = self.p_counter_frame if who == 'protagonist' else self.o_counter_frame
        tactic_var = self.protagonist_tactic if who == 'protagonist' else self.opponent_tactic
        counter_var = self.protagonist_counter if who == 'protagonist' else self.opponent_counter

        # 调试信息
        print(f"🔍 _update_tactics 调用: who={who}, theme_name='{theme_name}'")

        # 清空现有选项
        for widget in tactic_frame.winfo_children():
            widget.destroy()
        for widget in counter_frame.winfo_children():
            widget.destroy()

        # 从JSON文件中加载手段
        good_tactics = []
        bad_tactics = []

        # 尝试从对应能量文件夹中加载主题文件
        for energy_folder in ["生命力量", "精神力量", "思维力量", "物质资源", "信息资源", "关系资源"]:
            theme_file = f"组合能量/{energy_folder}/{theme_name}.json"
            try:
                with open(theme_file, 'r', encoding='utf-8') as f:
                    theme_data = json.load(f)
                    if "好手段" in theme_data and "坏手段" in theme_data:
                        good_tactics = theme_data["好手段"]
                        bad_tactics = theme_data["坏手段"]
                        print(f"✅ 从 {energy_folder} 加载主题 {theme_name} 的手段")
                        break
            except FileNotFoundError:
                continue
            except Exception as e:
                print(f"❌ 加载主题文件失败: {theme_file}, 错误: {e}")
                continue

        # 如果没有找到主题文件，使用通用手段
        if not good_tactics or not bad_tactics:
            good_tactics = ["努力", "学习", "合作", "服务", "贡献", "协商", "改进", "创新"]
            bad_tactics = ["争夺", "排挤", "陷害", "贿赂", "威胁", "造谣", "破坏", "报复"]
            print(f"❌ 主题 {theme_name} 未找到，使用通用手段")

        # 显示好手段选项
        for i, tactic in enumerate(good_tactics):
            rb = tk.Radiobutton(
                tactic_frame,
                text=tactic,
                variable=tactic_var,
                value=tactic,
                font=("微软雅黑", 9),
                anchor="w",
                wraplength=200,
                justify="left"
            )
            rb.grid(row=i//2, column=i%2, sticky="w", padx=3, pady=2)

        # 显示坏手段选项
        for i, counter in enumerate(bad_tactics):
            rb = tk.Radiobutton(
                counter_frame,
                text=counter,
                variable=counter_var,
                value=counter,
                font=("微软雅黑", 9),
                anchor="w",
                wraplength=200,
                justify="left"
            )
            rb.grid(row=i//2, column=i%2, sticky="w", padx=3, pady=2)

        # 设置默认选择
        if good_tactics:
            tactic_var.set(good_tactics[0])
        if bad_tactics:
            counter_var.set(bad_tactics[0])

    def _create_widgets(self):
        combo_module = ttk.LabelFrame(self, text="顶层设计", padding=10); combo_module.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(combo_module, text="第一步: 选择组合能量 (文件夹):", font=("微软雅黑", 11, "bold")).grid(row=0, column=0, columnspan=7, sticky="w", padx=5, pady=5)
        combinations = [f"对手{o} — 主角{p}" for o in self.themes for p in self.themes]
        canvas = tk.Canvas(combo_module, height=120); scrollbar = ttk.Scrollbar(combo_module, orient="vertical", command=canvas.yview); scrollable_frame = ttk.Frame(canvas)
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all"))); canvas.create_window((0, 0), window=scrollable_frame, anchor="nw"); canvas.configure(yscrollcommand=scrollbar.set)
        if combinations:
            num_cols = len(self.themes) or 1
            for i, combo in enumerate(combinations):
                rb = tk.Radiobutton(scrollable_frame, text=combo, variable=self.combination_var, value=combo, font=("微软雅黑", 9), anchor='w', wraplength=200, justify='left')
                rb.grid(row=i//num_cols, column=i%num_cols, sticky="w", padx=2, pady=1)
        canvas.grid(row=1, column=0, columnspan=6, sticky="nsew", padx=5, pady=5); scrollbar.grid(row=1, column=6, sticky="ns", pady=5)
        combo_module.grid_rowconfigure(1, weight=1)
        for i in range(7): combo_module.grid_columnconfigure(i, weight=1)
        
        ttk.Label(combo_module, text="第二步: 选择战略态势:", font=("微软雅黑", 11, "bold")).grid(row=2, column=0, columnspan=7, sticky="w", padx=5, pady=(20, 5))
        strategic_frame = ttk.Frame(combo_module); strategic_frame.grid(row=3, column=0, columnspan=7, sticky="ew", padx=5, pady=5)
        for i, stance in enumerate(self.strategic_stances):
            rb = tk.Radiobutton(strategic_frame, text=stance, variable=self.strategic_var, value=stance, font=("微软雅黑", 10), anchor='w', wraplength=300, justify='left')
            rb.grid(row=i//2, column=i%2, sticky="w", padx=10, pady=3)

        model_frame = ttk.LabelFrame(combo_module, text="情节模型选择", padding=5)
        model_frame.grid(row=4, column=0, columnspan=7, sticky="ew", padx=5, pady=(15,5))
        for model in self.plot_models:
            rb = ttk.Radiobutton(model_frame, text=model, variable=self.plot_model_var, value=model)
            rb.pack(side="left", padx=20)
        
        ttk.Label(combo_module, text="第三步: 选择博弈的完整顺序 (仅用于格式一):", font=("微软雅黑", 11, "bold")).grid(row=5, column=0, columnspan=7, sticky="w", padx=5, pady=(10, 5))
        self.tactical_frame = ttk.Frame(combo_module); self.tactical_frame.grid(row=6, column=0, columnspan=7, sticky="ew", padx=5, pady=5)
        for i, stance in enumerate(self.tactical_stances):
            rb = tk.Radiobutton(self.tactical_frame, text=stance, variable=self.tactical_var, value=stance, font=("微软雅黑", 9), anchor='w', wraplength=450, justify='left')
            rb.grid(row=i//4, column=i%4, sticky="w", padx=5, pady=3)

        modules_frame = tk.Frame(self); modules_frame.pack(fill="both", expand=True, padx=10, pady=5)
        o_container, o_scrollable_frame = self._create_scrolled_panel(modules_frame); o_container.pack(side="left", fill="both", expand=True, padx=(0, 5))
        self._create_module(o_scrollable_frame, "对手模块", self.opponent_energy_var, self.opponent_theme_var, self.opponent_situation, self.opponent_tactic, self.opponent_counter)
        p_container, p_scrollable_frame = self._create_scrolled_panel(modules_frame); p_container.pack(side="left", fill="both", expand=True, padx=(5, 0))
        self._create_module(p_scrollable_frame, "主角模块", self.protagonist_energy_var, self.protagonist_theme_var, self.protagonist_situation, self.protagonist_tactic, self.protagonist_counter)
        
        bottom_panel = tk.Frame(self); bottom_panel.pack(fill="x", padx=10, pady=(0, 5))
        result_frame = ttk.LabelFrame(bottom_panel, text="最终步: 生成", padding=10); result_frame.pack(fill="x")
        ttk.Button(result_frame, text="生成大纲", command=self.generate_full_outline, style="Accent.TButton").pack(side="left", padx=5)
        ttk.Label(result_frame, textvariable=self.result_var, font=("微软雅黑", 14, "bold"), foreground="blue").pack(side="left", padx=10)
        output_frame = ttk.LabelFrame(bottom_panel, text="大纲生成区", padding="10"); output_frame.pack(fill="both", expand=True, pady=(5,0))
        self.output_text = tk.Text(output_frame, height=10, wrap="word", font=("微软雅黑", 11)); self.output_text.pack(fill="both", expand=True)

    def _create_scrolled_panel(self, parent):
        container = tk.Frame(parent, bd=2, relief=tk.GROOVE)
        canvas = tk.Canvas(container); scrollbar = ttk.Scrollbar(container, orient="vertical", command=canvas.yview); scrollable_frame = ttk.Frame(canvas)
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all"))); canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set); canvas.pack(side="left", fill="both", expand=True); scrollbar.pack(side="right", fill="y")
        return container, scrollable_frame
    
    def _create_module(self, parent, title, energy_var, theme_var, situation_var, tactic_var, counter_var):
        module_frame = ttk.LabelFrame(parent, text=title, padding=10); module_frame.pack(fill="x", anchor='n')
        
        energy_frame = ttk.LabelFrame(module_frame, text="1. 选择能量 (文件夹)", padding=5); energy_frame.pack(fill="x", pady=5)
        if not self.themes:
            ttk.Label(energy_frame, text=f"未在 '{self.MASTER_ENERGY_FOLDER}' 中找到文件夹", foreground="red").pack()
        else:
            for i, theme_folder in enumerate(self.themes):
                # 添加回调函数来更新主题列表
                if "主角" in title:
                    command = lambda tf=theme_folder: [energy_var.set(tf), self._update_themes('protagonist')]
                else:
                    command = lambda tf=theme_folder: [energy_var.set(tf), self._update_themes('opponent')]

                rb = ttk.Radiobutton(energy_frame, text=theme_folder, variable=energy_var, value=theme_folder, command=command)
                rb.grid(row=i//4, column=i%4, sticky="w", padx=5, pady=2)
        
        theme_frame = ttk.LabelFrame(module_frame, text="2. 选择主题 (文件)", padding=5); theme_frame.pack(fill="x", pady=5)
        if '主角' in title: self.p_theme_frame = theme_frame
        else: self.o_theme_frame = theme_frame
            
        sit_frame = ttk.LabelFrame(module_frame, text="3. 选择境况", padding=5); sit_frame.pack(fill="x", pady=5)
        if '主角' in title: self.p_sit_frame = sit_frame
        else: self.o_sit_frame = sit_frame
        
        step4_frame = ttk.LabelFrame(module_frame, text="4. 选择手段", padding=5); step4_frame.pack(fill="x", pady=5)
        tactic_frame = ttk.LabelFrame(step4_frame, text="好手段选择", padding=5); tactic_frame.pack(fill="x", pady=2)
        if '主角' in title: self.p_tactic_frame = tactic_frame
        else: self.o_tactic_frame = tactic_frame
        # 不在初始化时显示手段，等待主题选择后再显示

        counter_frame = ttk.LabelFrame(step4_frame, text="坏手段选择", padding=5); counter_frame.pack(fill="x", pady=2)
        if '主角' in title: self.p_counter_frame = counter_frame
        else: self.o_counter_frame = counter_frame
        # 不在初始化时显示手段，等待主题选择后再显示
        
    def generate_full_outline(self):
        model = self.plot_model_var.get()
        p_sit = self.protagonist_situation.get(); o_sit = self.opponent_situation.get()
        p_tactic = self.protagonist_tactic.get(); p_counter = self.protagonist_counter.get()
        o_tactic = self.opponent_tactic.get(); o_counter = self.opponent_counter.get()
        strategic_stance = self.strategic_var.get(); tactical_stance = self.tactical_var.get()

        required_vars, outline_parts, final_result = [], {}, "未定义"

        if "格式一" in model:
            required_vars = [p_sit, o_sit, p_tactic, p_counter, o_tactic, o_counter, tactical_stance]
            if not all(required_vars):
                messagebox.showwarning("信息不全", "格式一需要填满所有选项，包括第三步的博弈顺序。")
                self.result_var.set("[ 信息不全 ]"); return
            outline_parts['开局'] = f"主角({p_sit}) vs 对手({o_sit})"
            p_type = "机遇" if "主角 机遇" in strategic_stance else "危机"; o_type = "机遇" if "对手 机遇" in strategic_stance else "危机"
            p_act_tone = "(好)" if "(好)" in p_tactic else "(坏)"; o_act_tone = "(好)" if "(好)" in o_tactic else "(坏)"
            p_act_for_key = f"自主{p_act_tone}"; o_act_for_key = f"自主{o_act_tone}"
            scenario_key = f"主角{p_type}_对手{o_type}"; action_key = f"{p_act_for_key}_vs_{o_act_for_key}"
            result_matrix = self.rules.get(scenario_key, {}); final_result = result_matrix.get(action_key, "未定义")
            choices = {"主角(对策)": p_tactic, "主角(反制)": p_counter, "对手(对策)": o_tactic, "对手(反制)": o_counter}
            sequence_parts = tactical_stance.split(" -> ")
            game_actions = [f"{part}({choices.get(part, '未知')})" for part in sequence_parts]
            outline_parts['博弈'] = "； ".join(game_actions) + "。"

        elif "格式四" in model:
            required_vars = [p_sit, p_tactic, o_counter]
            if not all(required_vars):
                messagebox.showwarning("信息不全", "格式四需要填写：主角境况、主角对策、对手反制。")
                self.result_var.set("[ 信息不全 ]"); return
            outline_parts['开局'] = f"主角({p_sit})"
            final_result = "成功" if "(好)" in p_tactic else "失败"
            outline_parts['博弈'] = f"主角对策({p_tactic})； 对手反制({o_counter})。"

        # 添加结果描述
        result_description = self.result_descriptions.get(final_result, "结果未知")
        detailed_result = f"【{final_result}】{result_description}"

        self.result_var.set(detailed_result)
        self.status_var.set(f"大纲已生成 ({model})。")
        
        outline = (f"开局: {outline_parts.get('开局', '')}\n"
                   f"博弈: {outline_parts.get('博弈', '')}\n"
                   f"结局: 【{final_result}】\n------------------------------------\n")
        self.output_text.insert(tk.END, outline)


class MainApplication:
    def __init__(self, root, rules_data):
        self.root = root
        self.root.title("小说博弈模拟器 (V8.1-修复版)")
        self.root.geometry("1800x950")
        self.status_var = tk.StringVar(value="请选择情节模型并开始构建。")
        style = ttk.Style(); style.configure("Accent.TButton", font=("微软雅黑", 10, "bold"))
        self.app_frame = PlottingSimulatorApp(root, self.status_var, rules_data)
        self.app_frame.pack(expand=True, fill='both')
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

if __name__ == "__main__":
    MASTER_FOLDER = "组合能量"; EXAMPLE_SUBFOLDER = os.path.join(MASTER_FOLDER, "测试选项"); EXAMPLE_JSON_FILE = os.path.join(EXAMPLE_SUBFOLDER, "主题A.json")
    if not os.path.exists(MASTER_FOLDER): os.makedirs(MASTER_FOLDER)
    if not os.path.exists(EXAMPLE_SUBFOLDER): os.makedirs(EXAMPLE_SUBFOLDER)
    if not os.path.exists(EXAMPLE_JSON_FILE):
        example_json_content = {"机遇": [{"theme_name": "主题A", "自主": {"name": "主题A自主机遇", "levels": {"低级": "A自主(低)"}},"互动": {"name": "主题A互动机遇", "levels": {"低级": "A互动(低)"}},"强制": {"name": "主题A强制机遇", "levels": {"低级": "A强制(低)"}},"运气": {"name": "主题A运气机遇", "levels": {"低级": "A运气(低)"}}}],"危机": [{"theme_name": "主题A", "自主": {"name": "主题A自主危机", "levels": {"低级": "A自主(低)"}},"互动": {"name": "主题A互动危机", "levels": {"低级": "A互动(低)"}},"强制": {"name": "主题A强制危机", "levels": {"低级": "A强制(低)"}},"运气": {"name": "主题A运气危机", "levels": {"低级": "A运气(低)"}}}]}
        with open(EXAMPLE_JSON_FILE, 'w', encoding='utf-8') as f: json.dump(example_json_content, f, ensure_ascii=False, indent=4)
    rules_data = {}
    try:
        with open("rules.json", 'r', encoding='utf-8') as f: rules_data = json.load(f).get("result_matrix", {})
    except Exception as e:
        temp_root = tk.Tk(); temp_root.withdraw(); messagebox.showerror("核心文件错误", f"加载 rules.json 失败。\n\n错误: {e}"); temp_root.destroy(); exit()
    root = tk.Tk()
    app = MainApplication(root, rules_data)
    root.mainloop()