# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import json
import os

def test_test_theme_loading():
    """专门测试'对手测试项 — 主角测试项'组合的加载"""
    
    root = tk.Tk()
    root.title("测试项主题加载测试")
    root.geometry("1200x800")
    
    print("🔍 测试'对手测试项 — 主角测试项'组合加载")
    print("=" * 60)
    
    # 模拟组合选择
    combo_selection = "对手测试项 — 主角测试项"
    strategic_selection = "对手 机遇 / 主角 机遇 (强强对抗)"
    
    print(f"📋 组合选择: {combo_selection}")
    print(f"📋 战略态势: {strategic_selection}")
    
    # 解析主题名称
    try:
        o_theme, p_theme = [s.strip() for s in combo_selection.split("—")]
        print(f"📋 解析结果: 对手='{o_theme}', 主角='{p_theme}'")
        
        # 提取纯主题名称
        o_theme_clean = o_theme.replace("对手", "")
        p_theme_clean = p_theme.replace("主角", "")
        print(f"📋 清理后: 对手='{o_theme_clean}', 主角='{p_theme_clean}'")
        
    except ValueError as e:
        print(f"❌ 解析失败: {e}")
        return
    
    # 确定境况类型
    p_type = "机遇" if "主角 机遇" in strategic_selection else "危机"
    o_type = "机遇" if "对手 机遇" in strategic_selection else "危机"
    
    print(f"📋 境况类型: 对手={o_type}, 主角={p_type}")
    
    # 确定文件名
    o_filename = "测试.json" if o_theme_clean == "测试项" else os.path.join("官场", f"{o_theme_clean}.json")
    p_filename = "测试.json" if p_theme_clean == "测试项" else os.path.join("官场", f"{p_theme_clean}.json")
    
    print(f"📋 文件路径: 对手={o_filename}, 主角={p_filename}")
    
    # 检查文件是否存在
    o_exists = os.path.exists(o_filename)
    p_exists = os.path.exists(p_filename)
    
    print(f"📋 文件存在: 对手={o_exists}, 主角={p_exists}")
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🔍 测试项主题加载测试", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=10)
    
    # 创建水平分割
    pane = tk.PanedWindow(main_frame, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=8)
    pane.pack(fill="both", expand=True)
    
    # 测试对手模块加载
    opponent_frame = tk.LabelFrame(pane, text="对手模块 - 测试项加载", font=("微软雅黑", 12, "bold"))
    pane.add(opponent_frame, minsize=550)
    
    opponent_status = tk.StringVar()
    opponent_situation = tk.StringVar()
    
    def load_opponent_data():
        try:
            with open(o_filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            situation_list = data.get(o_type, [])
            opponent_status.set(f"✅ 成功加载 {len(situation_list)} 个{o_type}境况")
            
            # 显示数据
            for widget in opponent_content.winfo_children():
                widget.destroy()
            
            title_label = tk.Label(opponent_content, text=f"{o_type}境况 - 全部展示:", font=("微软雅黑", 11, "bold"))
            title_label.pack(anchor="w", padx=5, pady=5)
            
            for i, item in enumerate(situation_list):
                name = item.get("name", "")
                item_frame = tk.LabelFrame(opponent_content, text=name, font=("微软雅黑", 10))
                item_frame.pack(fill="x", padx=5, pady=2)
                
                levels_frame = tk.Frame(item_frame)
                levels_frame.pack(fill="x", padx=5, pady=3)
                
                for j, (level, text) in enumerate(item.get("levels", {}).items()):
                    bg_color = "#FFE5E5" if "高" in level else "white"
                    rb = tk.Radiobutton(
                        levels_frame,
                        text=f"{level}: {text}",
                        variable=opponent_situation,
                        value=text,
                        font=("微软雅黑", 9),
                        anchor="w",
                        bg=bg_color
                    )
                    rb.grid(row=0, column=j, sticky="w", padx=3, pady=1)
            
            print(f"✅ 对手模块加载成功: {len(situation_list)} 个{o_type}境况")
            
        except Exception as e:
            opponent_status.set(f"❌ 加载失败: {e}")
            error_label = tk.Label(opponent_content, text=f"加载失败: {o_filename}\n错误: {e}", fg="red")
            error_label.pack(padx=5, pady=5)
            print(f"❌ 对手模块加载失败: {e}")
    
    # 对手模块界面
    tk.Label(opponent_frame, text=f"文件: {o_filename}", font=("微软雅黑", 10)).pack(pady=5)
    tk.Label(opponent_frame, text=f"主题: {o_theme_clean}", font=("微软雅黑", 10)).pack(pady=2)
    tk.Label(opponent_frame, text=f"类型: {o_type}", font=("微软雅黑", 10)).pack(pady=2)
    
    tk.Button(opponent_frame, text="加载对手数据", command=load_opponent_data, font=("微软雅黑", 10)).pack(pady=5)
    tk.Label(opponent_frame, textvariable=opponent_status, font=("微软雅黑", 10)).pack(pady=5)
    
    opponent_content = tk.Frame(opponent_frame)
    opponent_content.pack(fill="both", expand=True, padx=5, pady=5)
    
    # 测试主角模块加载
    protagonist_frame = tk.LabelFrame(pane, text="主角模块 - 测试项加载", font=("微软雅黑", 12, "bold"))
    pane.add(protagonist_frame, minsize=550)
    
    protagonist_status = tk.StringVar()
    protagonist_situation = tk.StringVar()
    
    def load_protagonist_data():
        try:
            with open(p_filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            situation_list = data.get(p_type, [])
            protagonist_status.set(f"✅ 成功加载 {len(situation_list)} 个{p_type}境况")
            
            # 显示数据
            for widget in protagonist_content.winfo_children():
                widget.destroy()
            
            title_label = tk.Label(protagonist_content, text=f"{p_type}境况 - 全部展示:", font=("微软雅黑", 11, "bold"))
            title_label.pack(anchor="w", padx=5, pady=5)
            
            for i, item in enumerate(situation_list):
                name = item.get("name", "")
                item_frame = tk.LabelFrame(protagonist_content, text=name, font=("微软雅黑", 10))
                item_frame.pack(fill="x", padx=5, pady=2)
                
                levels_frame = tk.Frame(item_frame)
                levels_frame.pack(fill="x", padx=5, pady=3)
                
                for j, (level, text) in enumerate(item.get("levels", {}).items()):
                    bg_color = "#FFE5E5" if "高" in level else "white"
                    rb = tk.Radiobutton(
                        levels_frame,
                        text=f"{level}: {text}",
                        variable=protagonist_situation,
                        value=text,
                        font=("微软雅黑", 9),
                        anchor="w",
                        bg=bg_color
                    )
                    rb.grid(row=0, column=j, sticky="w", padx=3, pady=1)
            
            print(f"✅ 主角模块加载成功: {len(situation_list)} 个{p_type}境况")
            
        except Exception as e:
            protagonist_status.set(f"❌ 加载失败: {e}")
            error_label = tk.Label(protagonist_content, text=f"加载失败: {p_filename}\n错误: {e}", fg="red")
            error_label.pack(padx=5, pady=5)
            print(f"❌ 主角模块加载失败: {e}")
    
    # 主角模块界面
    tk.Label(protagonist_frame, text=f"文件: {p_filename}", font=("微软雅黑", 10)).pack(pady=5)
    tk.Label(protagonist_frame, text=f"主题: {p_theme_clean}", font=("微软雅黑", 10)).pack(pady=2)
    tk.Label(protagonist_frame, text=f"类型: {p_type}", font=("微软雅黑", 10)).pack(pady=2)
    
    tk.Button(protagonist_frame, text="加载主角数据", command=load_protagonist_data, font=("微软雅黑", 10)).pack(pady=5)
    tk.Label(protagonist_frame, textvariable=protagonist_status, font=("微软雅黑", 10)).pack(pady=5)
    
    protagonist_content = tk.Frame(protagonist_frame)
    protagonist_content.pack(fill="both", expand=True, padx=5, pady=5)
    
    # 自动加载测试
    def auto_test():
        print("🚀 开始自动加载测试...")
        load_opponent_data()
        load_protagonist_data()
        print("✅ 自动加载测试完成")
    
    # 延迟执行自动测试
    root.after(1000, auto_test)
    
    print("=" * 60)
    print("✅ 测试界面创建完成")
    print("📝 将自动测试'对手测试项 — 主角测试项'组合的加载")
    
    root.mainloop()

if __name__ == "__main__":
    test_test_theme_loading()
