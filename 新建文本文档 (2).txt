# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
from tkinter import messagebox
import json
import os

class PlottingSimulatorApp(tk.Frame):
    def __init__(self, parent, status_var, rules_data):
        super().__init__(parent)
        self.status_var = status_var
        
        # --- Data Structures ---
        self.rules = rules_data
        self.themes = [
            "生命力量", "精神力量", "思维力量", 
            "物质资源", "信息资源", "关系资源", "测试项"
        ]
        self.action_types = [
            "自主-(好)", "互动-(好)", "强制-(好)", "运气-(好)",
            "自主-(坏)", "互动-(坏)", "强制-(坏)", "运气-(坏)"
        ]

        # --- UI Variables ---
        self.selected_theme_o = tk.StringVar()
        self.selected_theme_p = tk.StringVar()
        self.opponent_situation = tk.StringVar()
        self.protagonist_situation = tk.StringVar()
        self.protagonist_action = tk.StringVar()
        self.opponent_action = tk.StringVar()
        self.result_var = tk.StringVar(value="[ 点击'判定' ]")
        
        # --- UI Component References ---
        self.o_sit_options_frame = None
        self.p_sit_options_frame = None
        
        # --- Initialization ---
        self._create_widgets()

    def update_situations(self, who):
        # --- 修复：使用在 __init__ 中定义的正确变量名 ---
        theme_var = self.selected_theme_p if who == 'protagonist' else self.selected_theme_o
        frame = self.p_sit_options_frame if who == 'protagonist' else self.o_sit_options_frame
        variable = self.protagonist_situation if who == 'protagonist' else self.opponent_situation
        
        theme_name = theme_var.get()
        if not theme_name: return

        filename = "测试.json" if theme_name == "测试项" else os.path.join("官场", f"{theme_name}.json")
        
        for widget in frame.winfo_children(): widget.destroy()
        variable.set("")

        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            ttk.Label(frame, text="机遇:", font=("微软雅黑", 10, "bold")).pack(anchor="w", padx=5)
            for item in data.get("机遇", []):
                name = item.get("name", "")
                sub_frame = ttk.Frame(frame)
                sub_frame.pack(anchor="w", fill="x", padx=15)
                ttk.Label(sub_frame, text=f"{name}:", width=12).pack(side="left")
                for level, text in item.get("levels", {}).items():
                    ttk.Radiobutton(sub_frame, text=f"{level}: {text}", variable=variable, value=text).pack(side="left", padx=5)

            ttk.Label(frame, text="危机:", font=("微软雅黑", 10, "bold")).pack(anchor="w", pady=(10,0), padx=5)
            for item in data.get("危机", []):
                name = item.get("name", "")
                sub_frame = ttk.Frame(frame)
                sub_frame.pack(anchor="w", fill="x", padx=15)
                ttk.Label(sub_frame, text=f"{name}:", width=12).pack(side="left")
                for level, text in item.get("levels", {}).items():
                    ttk.Radiobutton(sub_frame, text=f"{level}: {text}", variable=variable, value=text).pack(side="left", padx=5)
            self.status_var.set(f"{os.path.basename(filename)} 加载成功！")
        except Exception as e:
            messagebox.showwarning("内容文件错误", f"加载 {filename} 失败。")

    def _create_widgets(self):
        main_pane = tk.PanedWindow(self, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=10)
        main_pane.pack(fill="both", expand=True)

        o_canvas, o_scrollable_frame = self._create_scrolled_panel(main_pane)
        self.opponent_module = self._create_module(o_scrollable_frame, "对手模块", self.selected_theme_o, self.opponent_situation, self.opponent_action, 'opponent')
        self.opponent_module.pack(fill="x")
        
        p_canvas, p_scrollable_frame = self._create_scrolled_panel(main_pane)
        self.protagonist_module = self._create_module(p_scrollable_frame, "主角模块", self.selected_theme_p, self.protagonist_situation, self.protagonist_action, 'protagonist')
        self.protagonist_module.pack(fill="x")

        right_panel = tk.Frame(self)
        main_pane.add(right_panel)
        
        step4_frame = ttk.LabelFrame(right_panel, text="最终步: 判定结果", padding=10)
        step4_frame.pack(fill="x", pady=5, padx=5)
        ttk.Button(step4_frame, text="判定结果", command=self.determine_result, style="Accent.TButton").pack(side="left", padx=5)
        ttk.Label(step4_frame, textvariable=self.result_var, font=("微软雅黑", 14, "bold"), foreground="blue").pack(side="left", padx=10)

        output_frame = ttk.LabelFrame(right_panel, text="大纲生成区", padding="10")
        output_frame.pack(fill="both", expand=True, pady=5, padx=5)
        self.output_text = tk.Text(output_frame, height=8, wrap="word", font=("微软雅黑", 11))
        self.output_text.pack(fill="both", expand=True, side="left", padx=2, pady=2)
        generate_button = ttk.Button(output_frame, text="生成\n大纲", command=self.generate_full_outline)
        generate_button.pack(padx=5, pady=5)

    def _create_scrolled_panel(self, parent):
        container = tk.Frame(parent, bd=2, relief=tk.GROOVE)
        parent.add(container, minsize=450)
        canvas = tk.Canvas(container)
        scrollbar = ttk.Scrollbar(container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        return canvas, scrollable_frame

    def _create_module(self, parent, title, theme_var, situation_var, action_var, who):
        module_frame = ttk.LabelFrame(parent, text=title, padding=10)
        
        step1_frame = ttk.LabelFrame(module_frame, text="第一步: 选择主题", padding=5)
        step1_frame.pack(fill="x", pady=5)
        theme_frame = ttk.Frame(step1_frame)
        theme_frame.pack(fill="x")
        for i, theme in enumerate(self.themes):
            ttk.Radiobutton(theme_frame, text=theme, variable=theme_var, value=theme, command=lambda w=who: self.update_situations(w)).grid(row=i//4, column=i%4, sticky="w")
        
        sit_frame = ttk.LabelFrame(module_frame, text="第二步: 选择境况", padding=5)
        sit_frame.pack(fill="x", pady=5)
        if who == 'protagonist': self.p_sit_options_frame = sit_frame
        else: self.o_sit_options_frame = sit_frame

        step3_frame = ttk.LabelFrame(module_frame, text="第三步: 选择对策", padding=5)
        step3_frame.pack(fill="x", pady=5)
        for atype in self.action_types:
            ttk.Radiobutton(step3_frame, text=atype, variable=action_var, value=atype.replace("-","")).pack(anchor="w")
            
        return module_frame
        
    def determine_result(self):
        p_sit_val = self.protagonist_situation.get()
        o_sit_val = self.opponent_situation.get()
        p_act = self.protagonist_action.get()
        o_act = self.opponent_action.get()

        if not all([p_sit_val, o_sit_val, p_act, o_act]):
            self.result_var.set("[ 信息不全 ]"); return
        
        p_type = self.find_situation_type(self.selected_theme_p.get(), p_sit_val)
        o_type = self.find_situation_type(self.selected_theme_o.get(), o_sit_val)

        if not p_type or not o_type:
            self.result_var.set("[ 境况错误 ]"); return

        scenario_key = f"主角{p_type}_对手{o_type}"
        action_key = f"{p_act}_vs_{o_act}"
        
        result_matrix = self.rules.get(scenario_key, {})
        final_result = result_matrix.get(action_key, "未定义")
        self.result_var.set(f"【{final_result}】")
        self.status_var.set(f"结果已判定: {final_result}")
        
    def find_situation_type(self, theme, sit_val):
        filename = "测试.json" if theme == "测试项" else os.path.join("官场", f"{theme}.json")
        try:
            with open(filename, 'r', encoding='utf-8') as f: data = json.load(f)
            for item in data.get("机遇", []):
                if sit_val in item.get("levels", {}).values(): return "机遇"
            for item in data.get("危机", []):
                if sit_val in item.get("levels", {}).values(): return "危机"
        except: return None
        return None

    def generate_full_outline(self):
        p_sit_spec = self.protagonist_situation.get()
        o_sit_spec = self.opponent_situation.get()
        p_act_type = self.protagonist_action.get()
        o_act_type = self.opponent_action.get()
        result = self.result_var.get()
        
        if not all([p_sit_spec, o_sit_spec, p_act_type, o_act_type]) or "[ " in result:
             messagebox.showwarning("提示", "请先选择所有条件并点击“判定结果”按钮。")
             return

        outline = (
            f"开局: 主角({p_sit_spec}) vs 对手({o_sit_spec})。\n"
            f"博弈: 主角采取({p_act_type})，对手以({o_act_type})反制。\n"
            f"结局: {result}\n"
            f"------------------------------------\n"
        )
        self.output_text.insert(tk.END, outline)
        self.status_var.set("大纲已追加到生成区！")


class MainApplication:
    def __init__(self, root, rules_data):
        self.root = root
        self.root.title("小说博弈模拟器 (双模块最终版)")
        self.root.geometry("1600x900")
        
        self.status_var = tk.StringVar(value="请为主角和对手布局并开始构建情节。")
        
        style = ttk.Style()
        style.configure("Accent.TButton", font=("微软雅黑", 10, "bold"))
        
        self.app_frame = PlottingSimulatorApp(root, self.status_var, rules_data)
        self.app_frame.pack(expand=True, fill='both', padx=5, pady=5)
            
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

if __name__ == "__main__":
    rules_data = {}
    try:
        with open("rules.json", 'r', encoding='utf-8') as f:
            rules_data = json.load(f).get("result_matrix", {})
    except Exception as e:
        temp_root = tk.Tk()
        temp_root.withdraw()
        messagebox.showerror("核心文件错误", f"加载 rules.json 失败，程序无法启动。\n\n错误: {e}")
        temp_root.destroy()
        exit()

    root = tk.Tk()
    app = MainApplication(root, rules_data)
    root.mainloop()