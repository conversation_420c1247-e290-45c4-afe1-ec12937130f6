# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import json

def test_simplified():
    """测试简化后的内容"""
    
    root = tk.Tk()
    root.title("简化内容测试")
    root.geometry("1000x700")
    
    print("🔍 测试简化后的内容")
    print("=" * 50)
    
    # 加载简化后的工作主题数据
    try:
        with open("组合能量/测试选项/主题A.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("✅ 成功加载简化后的主题A.json")
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return
    
    # 简化后的对策反制
    work_tactics = {
        "对策": [
            "庆祝升职", "拉拢同事", "展示能力", "寻求机会",
            "承认错误", "化解冲突", "减压调节", "危机应对"
        ],
        "反制": [
            "使绊子", "散布谣言", "抢夺功劳", "孤立对手",
            "揭发错误", "挑拨离间", "施加压力", "趁火打劫"
        ]
    }
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="📝 简化内容测试", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=10)
    
    # 创建三列布局
    columns_frame = tk.Frame(main_frame, bg="white")
    columns_frame.pack(fill="both", expand=True)
    
    # 第一列：境况
    situations_frame = tk.LabelFrame(columns_frame, text="🎯 工作境况（简化版）", font=("微软雅黑", 12, "bold"))
    situations_frame.pack(side="left", fill="both", expand=True, padx=5)
    
    # 显示机遇
    tk.Label(situations_frame, text="🌟 机遇", font=("微软雅黑", 11, "bold"), fg="#27AE60").pack(anchor="w", padx=5, pady=5)
    for theme_obj in data.get("机遇", []):
        for category in ["自主", "互动", "强制", "运气"]:
            category_data = theme_obj.get(category)
            if category_data:
                tk.Label(situations_frame, text=f"{category}型:", font=("微软雅黑", 10, "bold")).pack(anchor="w", padx=10)
                for level, text in category_data.get("levels", {}).items():
                    color = "#E74C3C" if level == "高级" else "#F39C12" if level == "中级" else "#27AE60"
                    tk.Label(situations_frame, text=f"  {level}: {text}", font=("微软雅黑", 9), fg=color).pack(anchor="w", padx=15)
    
    # 显示危机
    tk.Label(situations_frame, text="⚠️ 危机", font=("微软雅黑", 11, "bold"), fg="#E74C3C").pack(anchor="w", padx=5, pady=(10,5))
    for theme_obj in data.get("危机", []):
        for category in ["自主", "互动", "强制", "运气"]:
            category_data = theme_obj.get(category)
            if category_data:
                tk.Label(situations_frame, text=f"{category}型:", font=("微软雅黑", 10, "bold")).pack(anchor="w", padx=10)
                for level, text in category_data.get("levels", {}).items():
                    color = "#E74C3C" if level == "高级" else "#F39C12" if level == "中级" else "#27AE60"
                    tk.Label(situations_frame, text=f"  {level}: {text}", font=("微软雅黑", 9), fg=color).pack(anchor="w", padx=15)
    
    # 第二列：对策
    tactics_frame = tk.LabelFrame(columns_frame, text="🎯 工作对策（简化版）", font=("微软雅黑", 12, "bold"))
    tactics_frame.pack(side="left", fill="both", expand=True, padx=5)
    
    for i, tactic in enumerate(work_tactics["对策"]):
        tk.Label(tactics_frame, text=f"{i+1}. {tactic}", font=("微软雅黑", 10), fg="#27AE60").pack(anchor="w", padx=10, pady=2)
    
    # 第三列：反制
    counters_frame = tk.LabelFrame(columns_frame, text="⚔️ 工作反制（简化版）", font=("微软雅黑", 12, "bold"))
    counters_frame.pack(side="left", fill="both", expand=True, padx=5)
    
    for i, counter in enumerate(work_tactics["反制"]):
        tk.Label(counters_frame, text=f"{i+1}. {counter}", font=("微软雅黑", 10), fg="#E74C3C").pack(anchor="w", padx=10, pady=2)
    
    # 底部对比说明
    comparison_frame = tk.LabelFrame(main_frame, text="📊 简化对比", font=("微软雅黑", 12, "bold"))
    comparison_frame.pack(fill="x", pady=10)
    
    comparison_text = """
简化前 vs 简化后:

境况示例:
❌ 工作奖励：完成基础任务获得奖金或表扬  →  ✅ 工作奖励
❌ 升迁：因工作能力突出而获得职位提升    →  ✅ 升迁

对策示例:
❌ 庆祝升职：升职后举办庆祝活动，巩固地位  →  ✅ 庆祝升职
❌ 使绊子：对升职的同事进行暗中阻挠      →  ✅ 使绊子

优点: 简洁明了，一目了然，减少冗余信息
    """
    
    tk.Label(comparison_frame, text=comparison_text, font=("微软雅黑", 10), justify="left").pack(anchor="w", padx=10, pady=5)
    
    # 控制台输出
    print("\n📋 简化后的内容:")
    print("-" * 50)
    
    print("\n🌟 机遇境况:")
    for theme_obj in data.get("机遇", []):
        for category in ["自主", "互动", "强制", "运气"]:
            category_data = theme_obj.get(category)
            if category_data:
                print(f"  {category}型:")
                for level, text in category_data.get("levels", {}).items():
                    print(f"    {level}: {text}")
    
    print("\n⚠️ 危机境况:")
    for theme_obj in data.get("危机", []):
        for category in ["自主", "互动", "强制", "运气"]:
            category_data = theme_obj.get(category)
            if category_data:
                print(f"  {category}型:")
                for level, text in category_data.get("levels", {}).items():
                    print(f"    {level}: {text}")
    
    print("\n🎯 对策:")
    for i, tactic in enumerate(work_tactics["对策"], 1):
        print(f"  {i}. {tactic}")
    
    print("\n⚔️ 反制:")
    for i, counter in enumerate(work_tactics["反制"], 1):
        print(f"  {i}. {counter}")
    
    print("\n" + "=" * 50)
    print("✅ 简化测试完成")
    print("📝 所有内容都已简化，去掉了冒号后的详细描述")
    
    root.mainloop()

if __name__ == "__main__":
    test_simplified()
