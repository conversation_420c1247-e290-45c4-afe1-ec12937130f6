# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

def create_demo():
    """创建三步全展示演示界面"""
    
    root = tk.Tk()
    root.title("三步全展示组合模块演示")
    root.geometry("1400x1000")
    
    # 数据定义
    themes = ["生命力量", "精神力量", "思维力量", "物质资源", "信息资源", "关系资源", "测试项"]
    
    strategic_stances = [
        "对手 机遇 / 主角 机遇 (强强对抗)",
        "对手 危机 / 主角 机遇 (主角优势)",
        "对手 机遇 / 主角 危机 (主角劣势)",
        "对手 危机 / 主角 危机 (泥潭互搏)"
    ]
    
    tactical_stances = [
        "极限换伤 (主角进攻 vs 对手进攻)",
        "攻防大战 (主角进攻 vs 对手防守)",
        "生死时速 (主角防守 vs 对手进攻)",
        "各自为战 (主角防守 vs 对手防守)"
    ]
    
    # 生成组合选项
    combinations = []
    for o_theme in themes:
        for p_theme in themes:
            combinations.append(f"对手{o_theme} — 主角{p_theme}")
    
    print("🎉 三步全展示组合模块演示")
    print("=" * 50)
    print(f"📋 第一步: {len(combinations)} 个组合选项 (7×7网格)")
    print(f"🎯 第二步: {len(strategic_stances)} 个战略态势 (2×2网格)")
    print(f"⚔️  第三步: {len(tactical_stances)} 个战术姿态 (2×2网格)")
    print("=" * 50)
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=15, pady=15)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🎯 三步全展示组合模块", 
        font=("微软雅黑", 18, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=15)
    
    # 说明文字
    desc_label = tk.Label(
        main_frame,
        text="所有选项全部展示，无需下拉，一目了然，点击即选！",
        font=("微软雅黑", 12),
        bg="white",
        fg="#666666"
    )
    desc_label.pack(pady=5)
    
    # 选择变量
    combination_var = tk.StringVar()
    strategic_var = tk.StringVar()
    tactical_var = tk.StringVar()
    
    # 当前选择显示区域
    status_frame = tk.LabelFrame(main_frame, text="当前选择", font=("微软雅黑", 12, "bold"), bg="white")
    status_frame.pack(fill="x", pady=10)
    
    status_text = tk.Text(status_frame, height=4, font=("微软雅黑", 11), bg="#F8F9FA")
    status_text.pack(fill="x", padx=10, pady=10)
    
    def update_status():
        status_text.delete("1.0", tk.END)
        status_text.insert("1.0", f"✅ 组合能量: {combination_var.get()}\n")
        status_text.insert(tk.END, f"🎯 战略态势: {strategic_var.get()}\n")
        status_text.insert(tk.END, f"⚔️  战术姿态: {tactical_var.get()}\n")
        status_text.insert(tk.END, f"📊 选择完成度: {sum([bool(combination_var.get()), bool(strategic_var.get()), bool(tactical_var.get())])}/3")
    
    # 创建滚动区域
    canvas = tk.Canvas(main_frame, bg="white")
    scrollbar = tk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg="white")
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # 第一步：组合能量全展示
    step1_frame = tk.LabelFrame(
        scrollable_frame, 
        text="第一步: 选择组合能量 (49个选项全展示)", 
        font=("微软雅黑", 14, "bold"),
        bg="white",
        fg="#E74C3C"
    )
    step1_frame.pack(fill="x", pady=15, padx=10)
    
    combinations_frame = tk.Frame(step1_frame, bg="white")
    combinations_frame.pack(fill="x", padx=10, pady=10)
    
    for i, combination in enumerate(combinations):
        row = i // 7
        col = i % 7
        
        rb = tk.Radiobutton(
            combinations_frame,
            text=combination,
            variable=combination_var,
            value=combination,
            command=update_status,
            font=("微软雅黑", 9),
            anchor="w",
            bg="white",
            activebackground="#E3F2FD"
        )
        rb.grid(row=row, column=col, sticky="w", padx=2, pady=2)
    
    # 第二步：战略态势全展示
    step2_frame = tk.LabelFrame(
        scrollable_frame, 
        text="第二步: 选择战略态势 (4个选项全展示)", 
        font=("微软雅黑", 14, "bold"),
        bg="white",
        fg="#3498DB"
    )
    step2_frame.pack(fill="x", pady=15, padx=10)
    
    strategic_frame = tk.Frame(step2_frame, bg="white")
    strategic_frame.pack(fill="x", padx=10, pady=10)
    
    for i, stance in enumerate(strategic_stances):
        row = i // 2
        col = i % 2
        
        rb = tk.Radiobutton(
            strategic_frame,
            text=stance,
            variable=strategic_var,
            value=stance,
            command=update_status,
            font=("微软雅黑", 11),
            anchor="w",
            wraplength=400,
            justify="left",
            bg="white",
            activebackground="#E8F5E8"
        )
        rb.grid(row=row, column=col, sticky="w", padx=15, pady=5)
    
    # 第三步：战术姿态全展示
    step3_frame = tk.LabelFrame(
        scrollable_frame, 
        text="第三步: 选择战术姿态 (4个选项全展示)", 
        font=("微软雅黑", 14, "bold"),
        bg="white",
        fg="#9B59B6"
    )
    step3_frame.pack(fill="x", pady=15, padx=10)
    
    tactical_frame = tk.Frame(step3_frame, bg="white")
    tactical_frame.pack(fill="x", padx=10, pady=10)
    
    for i, stance in enumerate(tactical_stances):
        row = i // 2
        col = i % 2
        
        rb = tk.Radiobutton(
            tactical_frame,
            text=stance,
            variable=tactical_var,
            value=stance,
            command=update_status,
            font=("微软雅黑", 11),
            anchor="w",
            wraplength=400,
            justify="left",
            bg="white",
            activebackground="#FFF3E0"
        )
        rb.grid(row=row, column=col, sticky="w", padx=15, pady=5)
    
    # 布局滚动组件
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # 鼠标滚轮支持
    def on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    canvas.bind("<MouseWheel>", on_mousewheel)
    
    # 设置默认选择
    if combinations:
        combination_var.set(combinations[0])
    if strategic_stances:
        strategic_var.set(strategic_stances[0])
    if tactical_stances:
        tactical_var.set(tactical_stances[0])
    
    update_status()
    
    # 底部信息
    info_frame = tk.Frame(main_frame, bg="white")
    info_frame.pack(fill="x", pady=10)
    
    info_label = tk.Label(
        info_frame,
        text="🎉 所有选项全部展示完成！共计：49 + 4 + 4 = 57 个选项",
        font=("微软雅黑", 12, "bold"),
        bg="white",
        fg="#27AE60"
    )
    info_label.pack()
    
    print("✅ 演示界面创建完成！")
    print("📝 特点:")
    print("   - 所有选项全部展示，无需下拉")
    print("   - 三个步骤按顺序垂直排列")
    print("   - 支持鼠标滚轮滚动")
    print("   - 实时显示当前选择状态")
    print("   - 美观的颜色区分和布局")
    
    root.mainloop()

if __name__ == "__main__":
    create_demo()
