# -*- coding: utf-8 -*-

import os
import json

def get_theme_data():
    """获取所有60个主题的正确数据结构"""
    
    return {
        # 生命力量 (10个)
        "生命": {
            "机遇": {
                "自主": {"低级": "生命安稳", "中级": "生命无忧", "高级": "生命兴旺"},
                "互动": {"低级": "生命受护", "中级": "生命得助", "高级": "生命贵重"},
                "强制": {"低级": "生命遇险", "中级": "生命危急", "高级": "生命垂危"},
                "运气": {"低级": "生命偶安", "中级": "生命巧护", "高级": "生命天佑"}
            },
            "危机": {
                "自主": {"低级": "生命堪忧", "中级": "生命不保", "高级": "生命将尽"},
                "互动": {"低级": "生命受威", "中级": "生命被害", "高级": "生命遭劫"},
                "强制": {"低级": "生命受制", "中级": "生命被控", "高级": "生命操于人手"},
                "运气": {"低级": "生命多舛", "中级": "生命厄运", "高级": "生命劫数"}
            },
            "好手段": ["自保", "求助", "躲避", "防护", "治疗", "养生", "修身", "积德"],
            "坏手段": ["伤人", "杀戮", "下毒", "暗害", "陷害", "威胁", "绑架", "同归于尽"]
        },
        
        "安全": {
            "机遇": {
                "自主": {"低级": "安全稳固", "中级": "安全可靠", "高级": "安全无虞"},
                "互动": {"低级": "安全得护", "中级": "安全有保", "高级": "安全重重"},
                "强制": {"低级": "安全受考", "中级": "安全遇挑", "高级": "安全临考验"},
                "运气": {"低级": "安全偶得", "中级": "安全巧保", "高级": "安全天护"}
            },
            "危机": {
                "自主": {"低级": "安全松懈", "中级": "安全疏漏", "高级": "安全失守"},
                "互动": {"低级": "安全受扰", "中级": "安全被破", "高级": "安全全失"},
                "强制": {"低级": "安全受压", "中级": "安全被控", "高级": "安全沦陷"},
                "运气": {"低级": "安全意外", "中级": "安全突变", "高级": "安全崩塌"}
            },
            "好手段": ["防范", "警戒", "巡查", "保护", "加固", "预警", "演练", "备案"],
            "坏手段": ["破坏", "渗透", "偷袭", "暗算", "爆破", "纵火", "投毒", "恐吓"]
        },
        
        "地位": {
            "机遇": {
                "自主": {"低级": "地位稳固", "中级": "地位提升", "高级": "地位显赫"},
                "互动": {"低级": "地位获认", "中级": "地位受推", "高级": "地位众望"},
                "强制": {"低级": "地位受考", "中级": "地位遇挑", "高级": "地位临大考"},
                "运气": {"低级": "地位偶升", "中级": "地位巧得", "高级": "地位天赐"}
            },
            "危机": {
                "自主": {"低级": "地位动摇", "中级": "地位下滑", "高级": "地位失落"},
                "互动": {"低级": "地位受质", "中级": "地位被挑", "高级": "地位众叛"},
                "强制": {"低级": "地位受压", "中级": "地位被夺", "高级": "地位全失"},
                "运气": {"低级": "地位意外", "中级": "地位突变", "高级": "地位暴跌"}
            },
            "好手段": ["努力", "表现", "学习", "协作", "服务", "贡献", "谦逊", "修德"],
            "坏手段": ["争夺", "排挤", "陷害", "贿赂", "威胁", "造谣", "拉帮", "结派"]
        },
        
        "发展": {
            "机遇": {
                "自主": {"低级": "发展平稳", "中级": "发展良好", "高级": "发展迅猛"},
                "互动": {"低级": "发展得助", "中级": "发展受推", "高级": "发展众扶"},
                "强制": {"低级": "发展受阻", "中级": "发展遇挫", "高级": "发展临考验"},
                "运气": {"低级": "发展偶顺", "中级": "发展巧遇", "高级": "发展天助"}
            },
            "危机": {
                "自主": {"低级": "发展缓慢", "中级": "发展停滞", "高级": "发展倒退"},
                "互动": {"低级": "发展受阻", "中级": "发展被阻", "高级": "发展全阻"},
                "强制": {"低级": "发展受限", "中级": "发展被限", "高级": "发展禁止"},
                "运气": {"低级": "发展不顺", "中级": "发展厄运", "高级": "发展劫难"}
            },
            "好手段": ["规划", "学习", "创新", "合作", "投资", "改革", "开拓", "进取"],
            "坏手段": ["阻挠", "破坏", "垄断", "排斥", "打压", "封锁", "掠夺", "窃取"]
        },
        
        "机会": {
            "机遇": {
                "自主": {"低级": "机会出现", "中级": "机会良好", "高级": "机会绝佳"},
                "互动": {"低级": "机会获知", "中级": "机会得助", "高级": "机会共享"},
                "强制": {"低级": "机会稍纵", "中级": "机会竞争", "高级": "机会争夺"},
                "运气": {"低级": "机会偶遇", "中级": "机会巧合", "高级": "机会天赐"}
            },
            "危机": {
                "自主": {"低级": "机会错失", "中级": "机会失去", "高级": "机会断绝"},
                "互动": {"低级": "机会被夺", "中级": "机会被抢", "高级": "机会被断"},
                "强制": {"低级": "机会受阻", "中级": "机会被阻", "高级": "机会全无"},
                "运气": {"低级": "机会不济", "中级": "机会厄运", "高级": "机会绝望"}
            },
            "好手段": ["把握", "创造", "等待", "寻找", "准备", "争取", "合作", "分享"],
            "坏手段": ["抢夺", "破坏", "垄断", "欺骗", "威胁", "排挤", "暗算", "独占"]
        },
        
        "健康": {
            "机遇": {
                "自主": {"低级": "健康尚可", "中级": "健康良好", "高级": "健康强壮"},
                "互动": {"低级": "健康得护", "中级": "健康受助", "高级": "健康众护"},
                "强制": {"低级": "健康受考", "中级": "健康遇挑", "高级": "健康临考验"},
                "运气": {"低级": "健康偶好", "中级": "健康巧复", "高级": "健康天佑"}
            },
            "危机": {
                "自主": {"低级": "健康欠佳", "中级": "健康不良", "高级": "健康恶化"},
                "互动": {"低级": "健康受损", "中级": "健康被害", "高级": "健康全毁"},
                "强制": {"低级": "健康受制", "中级": "健康被控", "高级": "健康操于人手"},
                "运气": {"低级": "健康不济", "中级": "健康厄运", "高级": "健康劫数"}
            },
            "好手段": ["养生", "治疗", "锻炼", "调理", "休息", "保健", "预防", "修身"],
            "坏手段": ["下毒", "暗害", "折磨", "摧残", "虐待", "感染", "诅咒", "报复"]
        },
        
        "底线": {
            "机遇": {
                "自主": {"低级": "底线坚守", "中级": "底线明确", "高级": "底线崇高"},
                "互动": {"低级": "底线获认", "中级": "底线受敬", "高级": "底线众仰"},
                "强制": {"低级": "底线受考", "中级": "底线遇挑", "高级": "底线临大考"},
                "运气": {"低级": "底线偶守", "中级": "底线巧护", "高级": "底线天助"}
            },
            "危机": {
                "自主": {"低级": "底线模糊", "中级": "底线动摇", "高级": "底线崩塌"},
                "互动": {"低级": "底线受质", "中级": "底线被挑", "高级": "底线众弃"},
                "强制": {"低级": "底线受压", "中级": "底线被迫", "高级": "底线全破"},
                "运气": {"低级": "底线意外", "中级": "底线突破", "高级": "底线毁灭"}
            },
            "好手段": ["坚守", "维护", "宣扬", "教育", "感化", "示范", "牺牲", "殉道"],
            "坏手段": ["突破", "践踏", "摧毁", "腐蚀", "诱惑", "威胁", "强迫", "毁灭"]
        },
        
        "退路": {
            "机遇": {
                "自主": {"低级": "退路尚存", "中级": "退路通畅", "高级": "退路宽广"},
                "互动": {"低级": "退路获助", "中级": "退路得保", "高级": "退路众护"},
                "强制": {"低级": "退路受阻", "中级": "退路遇险", "高级": "退路临绝"},
                "运气": {"低级": "退路偶现", "中级": "退路巧开", "高级": "退路天开"}
            },
            "危机": {
                "自主": {"低级": "退路狭窄", "中级": "退路受阻", "高级": "退路断绝"},
                "互动": {"低级": "退路被阻", "中级": "退路被断", "高级": "退路全无"},
                "强制": {"低级": "退路受制", "中级": "退路被控", "高级": "退路操于人手"},
                "运气": {"低级": "退路不济", "中级": "退路厄运", "高级": "退路绝望"}
            },
            "好手段": ["保留", "开辟", "维护", "拓展", "准备", "规划", "协商", "妥协"],
            "坏手段": ["断绝", "封锁", "破坏", "控制", "威胁", "要挟", "逼迫", "绝杀"]
        },
        
        "潜力": {
            "机遇": {
                "自主": {"低级": "潜力初现", "中级": "潜力显露", "高级": "潜力爆发"},
                "互动": {"低级": "潜力被识", "中级": "潜力受赏", "高级": "潜力众认"},
                "强制": {"低级": "潜力受考", "中级": "潜力遇挑", "高级": "潜力临考验"},
                "运气": {"低级": "潜力偶现", "中级": "潜力巧发", "高级": "潜力天赐"}
            },
            "危机": {
                "自主": {"低级": "潜力埋没", "中级": "潜力受阻", "高级": "潜力枯竭"},
                "互动": {"低级": "潜力被忽", "中级": "潜力被压", "高级": "潜力被毁"},
                "强制": {"低级": "潜力受制", "中级": "潜力被控", "高级": "潜力操于人手"},
                "运气": {"低级": "潜力不济", "中级": "潜力厄运", "高级": "潜力劫数"}
            },
            "好手段": ["发掘", "培养", "锻炼", "展示", "发挥", "释放", "激发", "成就"],
            "坏手段": ["埋没", "压制", "摧毁", "窃取", "利用", "榨取", "浪费", "毁灭"]
        },
        
        "任期": {
            "机遇": {
                "自主": {"低级": "任期稳定", "中级": "任期延长", "高级": "任期无限"},
                "互动": {"低级": "任期获认", "中级": "任期受推", "高级": "任期众望"},
                "强制": {"低级": "任期受考", "中级": "任期遇挑", "高级": "任期临大考"},
                "运气": {"低级": "任期偶延", "中级": "任期巧续", "高级": "任期天赐"}
            },
            "危机": {
                "自主": {"低级": "任期将满", "中级": "任期结束", "高级": "任期终结"},
                "互动": {"低级": "任期受质", "中级": "任期被挑", "高级": "任期众弃"},
                "强制": {"低级": "任期受限", "中级": "任期被限", "高级": "任期全无"},
                "运气": {"低级": "任期不济", "中级": "任期厄运", "高级": "任期劫数"}
            },
            "好手段": ["履职", "服务", "贡献", "传承", "交接", "总结", "规划", "退让"],
            "坏手段": ["恋权", "贪腐", "专权", "独裁", "延任", "篡权", "毁灭", "报复"]
        }
    }

def regenerate_all_themes():
    """重新生成所有主题的正确结构"""
    
    print("🔧 开始重新生成所有主题的正确结构")
    print("=" * 50)
    
    theme_data = get_theme_data()
    
    # 只处理生命力量的10个主题作为示例
    energy_folder = "组合能量/生命力量"
    
    if not os.path.exists(energy_folder):
        print(f"❌ 文件夹不存在: {energy_folder}")
        return
    
    print(f"\n📁 处理 生命力量")
    
    for theme_name, data in theme_data.items():
        file_path = os.path.join(energy_folder, f"{theme_name}.json")
        
        print(f"  📝 生成 {theme_name}.json")
        
        # 构建新的JSON结构
        new_structure = {
            "机遇": [{
                "theme_name": theme_name,
                "自主": {
                    "name": f"{theme_name}机遇",
                    "levels": data["机遇"]["自主"]
                },
                "互动": {
                    "name": f"{theme_name}机遇", 
                    "levels": data["机遇"]["互动"]
                },
                "强制": {
                    "name": f"{theme_name}机遇",
                    "levels": data["机遇"]["强制"]
                },
                "运气": {
                    "name": f"{theme_name}机遇",
                    "levels": data["机遇"]["运气"]
                }
            }],
            "危机": [{
                "theme_name": theme_name,
                "自主": {
                    "name": f"{theme_name}危机",
                    "levels": data["危机"]["自主"]
                },
                "互动": {
                    "name": f"{theme_name}危机",
                    "levels": data["危机"]["互动"]
                },
                "强制": {
                    "name": f"{theme_name}危机",
                    "levels": data["危机"]["强制"]
                },
                "运气": {
                    "name": f"{theme_name}危机",
                    "levels": data["危机"]["运气"]
                }
            }],
            "好手段": data["好手段"],
            "坏手段": data["坏手段"]
        }
        
        try:
            # 保存新文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(new_structure, f, ensure_ascii=False, indent=4)
            
            print(f"    ✅ 生成成功")
            
        except Exception as e:
            print(f"    ❌ 生成失败: {e}")
    
    print("\n" + "=" * 50)
    print("✅ 生命力量主题重新生成完成")
    print("📋 新结构包含:")
    print("  - 境况：具体状态（如'生命安稳'、'生命将尽'）")
    print("  - 手段：具体行动（如'自保'、'伤人'）")

if __name__ == "__main__":
    regenerate_all_themes()
