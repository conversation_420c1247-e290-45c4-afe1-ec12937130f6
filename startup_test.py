# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import json
import os

def test_startup():
    """测试程序启动功能"""
    
    print("🔍 测试程序启动功能")
    print("=" * 50)
    
    # 检查必要文件
    required_files = ["rules.json", "测试.json"]
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - 存在")
        else:
            print(f"❌ {file} - 缺失")
            missing_files.append(file)
    
    if missing_files:
        print(f"⚠️  缺失文件: {missing_files}")
        return False
    
    # 测试JSON文件加载
    try:
        with open("rules.json", 'r', encoding='utf-8') as f:
            rules_data = json.load(f)
        print("✅ rules.json - 加载成功")
    except Exception as e:
        print(f"❌ rules.json - 加载失败: {e}")
        return False
    
    try:
        with open("测试.json", 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        print("✅ 测试.json - 加载成功")
    except Exception as e:
        print(f"❌ 测试.json - 加载失败: {e}")
        return False
    
    # 创建简单的GUI测试
    root = tk.Tk()
    root.title("启动测试")
    root.geometry("600x400")
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🎉 程序启动测试成功！", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#27AE60"
    )
    title_label.pack(pady=20)
    
    # 测试结果
    result_text = "✅ 所有必要文件都存在并可以正常加载\n"
    result_text += "✅ JSON数据格式正确\n"
    result_text += "✅ GUI界面可以正常创建\n"
    result_text += "✅ 程序可以正常启动"
    
    result_label = tk.Label(
        main_frame,
        text=result_text,
        font=("微软雅黑", 12),
        bg="white",
        justify="left"
    )
    result_label.pack(pady=20)
    
    # 启动主程序按钮
    def launch_main():
        root.destroy()
        print("🚀 启动主程序...")
        os.system('python "A大纲生成 - 副本.py"')
    
    launch_button = tk.Button(
        main_frame,
        text="启动主程序",
        command=launch_main,
        font=("微软雅黑", 12, "bold"),
        bg="#3498DB",
        fg="white",
        padx=20,
        pady=10
    )
    launch_button.pack(pady=20)
    
    # 说明
    info_label = tk.Label(
        main_frame,
        text="如果看到这个界面，说明程序环境正常，可以点击按钮启动主程序",
        font=("微软雅黑", 10),
        bg="white",
        fg="#666666"
    )
    info_label.pack(pady=10)
    
    print("=" * 50)
    print("✅ 启动测试完成，程序环境正常")
    print("📝 如果主程序无法启动，请检查:")
    print("   1. Python环境是否正确")
    print("   2. tkinter模块是否可用")
    print("   3. 文件路径是否正确")
    print("   4. 文件权限是否足够")
    
    root.mainloop()
    return True

if __name__ == "__main__":
    test_startup()
