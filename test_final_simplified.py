# -*- coding: utf-8 -*-

import tkinter as tk
import json

def test_final_simplified():
    """测试完全简化后的内容"""
    
    root = tk.Tk()
    root.title("完全简化测试")
    root.geometry("800x600")
    
    print("🔍 测试完全简化后的内容")
    print("=" * 50)
    
    # 加载完全简化后的数据
    try:
        with open("组合能量/测试选项/主题A.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("✅ 成功加载完全简化后的主题A.json")
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="✂️ 完全简化测试", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=10)
    
    # 创建滚动文本框显示内容
    text_frame = tk.Frame(main_frame, bg="white")
    text_frame.pack(fill="both", expand=True)
    
    text_widget = tk.Text(text_frame, font=("微软雅黑", 11), wrap="word")
    scrollbar = tk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)
    
    # 显示完全简化后的内容
    content = "🎯 完全简化后的工作主题内容\n"
    content += "=" * 50 + "\n\n"
    
    # 机遇部分
    content += "🌟 工作机遇\n"
    content += "-" * 30 + "\n"
    for theme_obj in data.get("机遇", []):
        for category in ["自主", "互动", "强制", "运气"]:
            category_data = theme_obj.get(category)
            if category_data:
                name = category_data.get("name", "")
                content += f"\n📋 {name}\n"
                for level, text in category_data.get("levels", {}).items():
                    content += f"  {level}: {text}\n"
    
    # 危机部分
    content += "\n⚠️ 工作危机\n"
    content += "-" * 30 + "\n"
    for theme_obj in data.get("危机", []):
        for category in ["自主", "互动", "强制", "运气"]:
            category_data = theme_obj.get(category)
            if category_data:
                name = category_data.get("name", "")
                content += f"\n📋 {name}\n"
                for level, text in category_data.get("levels", {}).items():
                    content += f"  {level}: {text}\n"
    
    # 对策反制部分
    work_tactics = {
        "对策": [
            "庆祝升职", "拉拢同事", "展示能力", "寻求机会",
            "承认错误", "化解冲突", "减压调节", "危机应对"
        ],
        "反制": [
            "使绊子", "散布谣言", "抢夺功劳", "孤立对手",
            "揭发错误", "挑拨离间", "施加压力", "趁火打劫"
        ]
    }
    
    content += "\n🎯 工作对策\n"
    content += "-" * 30 + "\n"
    for i, tactic in enumerate(work_tactics["对策"], 1):
        content += f"{i:2d}. {tactic}\n"
    
    content += "\n⚔️ 工作反制\n"
    content += "-" * 30 + "\n"
    for i, counter in enumerate(work_tactics["反制"], 1):
        content += f"{i:2d}. {counter}\n"
    
    # 简化对比
    content += "\n📊 简化对比\n"
    content += "=" * 50 + "\n"
    content += "\n简化前 vs 简化后:\n\n"
    content += "境况名称:\n"
    content += "❌ 工作的自主机遇  →  ✅ 自主机遇\n"
    content += "❌ 工作的互动危机  →  ✅ 互动危机\n\n"
    content += "境况选项:\n"
    content += "❌ 升迁：因工作能力突出而获得职位提升  →  ✅ 升迁\n"
    content += "❌ 工作刁难：被同事或上级故意刁难  →  ✅ 工作刁难\n\n"
    content += "对策反制:\n"
    content += "❌ 庆祝升职：升职后举办庆祝活动  →  ✅ 庆祝升职\n"
    content += "❌ 使绊子：对升职的同事进行阻挠  →  ✅ 使绊子\n\n"
    content += "✅ 优点: 简洁明了，一目了然，没有冗余信息\n"
    
    # 插入内容
    text_widget.insert("1.0", content)
    text_widget.config(state="disabled")  # 设为只读
    
    # 放置组件
    text_widget.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # 控制台输出
    print("\n📋 完全简化后的内容:")
    print("-" * 50)
    
    print("\n🌟 机遇境况名称:")
    for theme_obj in data.get("机遇", []):
        for category in ["自主", "互动", "强制", "运气"]:
            category_data = theme_obj.get(category)
            if category_data:
                name = category_data.get("name", "")
                print(f"  {name}")
    
    print("\n⚠️ 危机境况名称:")
    for theme_obj in data.get("危机", []):
        for category in ["自主", "互动", "强制", "运气"]:
            category_data = theme_obj.get(category)
            if category_data:
                name = category_data.get("name", "")
                print(f"  {name}")
    
    print("\n🎯 对策:")
    for tactic in work_tactics["对策"]:
        print(f"  {tactic}")
    
    print("\n⚔️ 反制:")
    for counter in work_tactics["反制"]:
        print(f"  {counter}")
    
    print("\n" + "=" * 50)
    print("✅ 完全简化测试完成")
    print("📝 所有内容都已完全简化，去掉了所有冒号后的描述")
    
    root.mainloop()

if __name__ == "__main__":
    test_final_simplified()
