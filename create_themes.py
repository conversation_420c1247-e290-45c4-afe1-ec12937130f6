# -*- coding: utf-8 -*-

import json
import os

def create_theme_files():
    """根据参考文件创建所有主题文件"""
    
    print("🔧 开始创建主题文件")
    print("=" * 50)
    
    # 读取参考文件
    reference_files = {
        "生命力量": "参考/生命力量.json",
        "精神力量": "参考/精神力量.json", 
        "思维力量": "参考/思维力量.json",
        "物质资源": "参考/物质资源.json",
        "信息资源": "参考/信息资源.json",
        "关系资源": "参考/关系资源.json"
    }
    
    for energy_type, ref_file in reference_files.items():
        print(f"\n📁 处理 {energy_type}")
        
        try:
            with open(ref_file, 'r', encoding='utf-8') as f:
                ref_data = json.load(f)
            
            conflict_list = ref_data.get("conflict_list", [])
            print(f"  找到 {len(conflict_list)} 个主题")
            
            # 为每个crisis_name创建独立的json文件
            for conflict in conflict_list:
                crisis_name = conflict.get("crisis_name", "")
                if not crisis_name:
                    continue
                
                print(f"    创建: {crisis_name}.json")
                
                # 创建新的主题文件结构
                theme_data = {
                    "机遇": [{
                        "theme_name": crisis_name,
                        "自主": {"name": "自主机遇", "levels": {"低级": "", "中级": "", "高级": ""}},
                        "互动": {"name": "互动机遇", "levels": {"低级": "", "中级": "", "高级": ""}},
                        "强制": {"name": "强制机遇", "levels": {"低级": "", "中级": "", "高级": ""}},
                        "运气": {"name": "运气机遇", "levels": {"低级": "", "中级": "", "高级": ""}}
                    }],
                    "危机": [{
                        "theme_name": crisis_name,
                        "自主": {"name": "自主危机", "levels": {"低级": "", "中级": "", "高级": ""}},
                        "互动": {"name": "互动危机", "levels": {"低级": "", "中级": "", "高级": ""}},
                        "强制": {"name": "强制危机", "levels": {"低级": "", "中级": "", "高级": ""}},
                        "运气": {"name": "运气危机", "levels": {"低级": "", "中级": "", "高级": ""}}
                    }]
                }
                
                # 从参考数据中提取action_name填充到levels中
                actions = conflict.get("actions", [])
                
                # 按action_type分类
                action_types = {
                    "自己付出": [],
                    "相互付出": [],
                    "强制付出": [],
                    "运气": []
                }
                
                for action in actions:
                    action_name = action.get("action_name", "")
                    action_type = action.get("action_type", "")
                    if action_name and action_type in action_types:
                        action_types[action_type].append(action_name)
                
                # 填充机遇数据
                if len(action_types["自己付出"]) >= 3:
                    theme_data["机遇"][0]["自主"]["levels"]["低级"] = action_types["自己付出"][0]
                    theme_data["机遇"][0]["自主"]["levels"]["中级"] = action_types["自己付出"][1]
                    theme_data["机遇"][0]["自主"]["levels"]["高级"] = action_types["自己付出"][2]
                
                if len(action_types["相互付出"]) >= 3:
                    theme_data["机遇"][0]["互动"]["levels"]["低级"] = action_types["相互付出"][0]
                    theme_data["机遇"][0]["互动"]["levels"]["中级"] = action_types["相互付出"][1]
                    theme_data["机遇"][0]["互动"]["levels"]["高级"] = action_types["相互付出"][2]
                
                if len(action_types["强制付出"]) >= 3:
                    theme_data["机遇"][0]["强制"]["levels"]["低级"] = action_types["强制付出"][0]
                    theme_data["机遇"][0]["强制"]["levels"]["中级"] = action_types["强制付出"][1]
                    theme_data["机遇"][0]["强制"]["levels"]["高级"] = action_types["强制付出"][2]
                
                # 运气机遇使用通用内容
                theme_data["机遇"][0]["运气"]["levels"]["低级"] = "意外收获"
                theme_data["机遇"][0]["运气"]["levels"]["中级"] = "贵人相助"
                theme_data["机遇"][0]["运气"]["levels"]["高级"] = "天降大任"
                
                # 危机数据使用通用模板
                theme_data["危机"][0]["自主"]["levels"]["低级"] = "计划失误"
                theme_data["危机"][0]["自主"]["levels"]["中级"] = "能力不足"
                theme_data["危机"][0]["自主"]["levels"]["高级"] = "走投无路"
                
                theme_data["危机"][0]["互动"]["levels"]["低级"] = "被人出卖"
                theme_data["危机"][0]["互动"]["levels"]["中级"] = "盟友背叛"
                theme_data["危机"][0]["互动"]["levels"]["高级"] = "众叛亲离"
                
                theme_data["危机"][0]["强制"]["levels"]["低级"] = "被迫妥协"
                theme_data["危机"][0]["强制"]["levels"]["中级"] = "陷入绝境"
                theme_data["危机"][0]["强制"]["levels"]["高级"] = "生死一线"
                
                theme_data["危机"][0]["运气"]["levels"]["低级"] = "倒霉事件"
                theme_data["危机"][0]["运气"]["levels"]["中级"] = "背黑锅"
                theme_data["危机"][0]["运气"]["levels"]["高级"] = "天灾人祸"
                
                # 保存文件
                file_path = f"组合能量/{energy_type}/{crisis_name}.json"
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(theme_data, f, ensure_ascii=False, indent=4)
                
        except Exception as e:
            print(f"❌ 处理 {energy_type} 时出错: {e}")
    
    print("\n" + "=" * 50)
    print("✅ 主题文件创建完成")

if __name__ == "__main__":
    create_theme_files()
