# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

def test_theme_selection():
    """测试主题选择的全展示效果"""
    
    root = tk.Tk()
    root.title("主题选择全展示测试")
    root.geometry("1400x800")
    
    # 主题数据
    themes = ["生命力量", "精神力量", "思维力量", "物质资源", "信息资源", "关系资源", "测试项"]
    
    print("🎯 测试主题选择全展示")
    print("=" * 50)
    print(f"📋 主题数量: {len(themes)} 个")
    print(f"📋 主题列表: {', '.join(themes)}")
    print("=" * 50)
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=15, pady=15)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🎯 模块第一步：主题选择全展示测试", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=15)
    
    # 创建水平分割，模拟对手和主角模块
    modules_pane = tk.PanedWindow(main_frame, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=8)
    modules_pane.pack(fill="both", expand=True)
    
    # 选择变量
    opponent_theme_var = tk.StringVar()
    protagonist_theme_var = tk.StringVar()
    
    # 状态显示
    def update_status():
        status_text.delete("1.0", tk.END)
        status_text.insert("1.0", f"对手模块选择: {opponent_theme_var.get()}\n")
        status_text.insert(tk.END, f"主角模块选择: {protagonist_theme_var.get()}\n")
        status_text.insert(tk.END, f"选择完成度: {sum([bool(opponent_theme_var.get()), bool(protagonist_theme_var.get())])}/2")
    
    # 对手模块
    opponent_frame = tk.LabelFrame(modules_pane, text="对手模块", font=("微软雅黑", 14, "bold"), bg="white")
    modules_pane.add(opponent_frame, minsize=600)
    
    # 对手模块 - 第一步：选择具体主题
    opponent_theme_frame = tk.LabelFrame(
        opponent_frame, 
        text="第一步: 选择具体主题 (7个选项全展示)", 
        font=("微软雅黑", 12, "bold"),
        bg="white",
        fg="#E74C3C"
    )
    opponent_theme_frame.pack(fill="x", padx=10, pady=10)
    
    # 对手主题选择网格
    opponent_grid_frame = tk.Frame(opponent_theme_frame, bg="white")
    opponent_grid_frame.pack(fill="x", padx=10, pady=10)
    
    for i, theme in enumerate(themes):
        row = i // 4  # 每行4个，共2行
        col = i % 4
        
        rb = tk.Radiobutton(
            opponent_grid_frame,
            text=theme,
            variable=opponent_theme_var,
            value=theme,
            command=update_status,
            font=("微软雅黑", 11),
            anchor="w",
            bg="white",
            activebackground="#FFEBEE"
        )
        rb.grid(row=row, column=col, sticky="w", padx=8, pady=5)
    
    # 对手模块 - 第二步和第三步的占位符
    opponent_step2_frame = tk.LabelFrame(opponent_frame, text="第二步: 选择具体境况", font=("微软雅黑", 11), bg="white")
    opponent_step2_frame.pack(fill="x", padx=10, pady=5)
    tk.Label(opponent_step2_frame, text="等待选择主题后显示境况选项...", font=("微软雅黑", 10), bg="white", fg="gray").pack(pady=10)
    
    opponent_step3_frame = tk.LabelFrame(opponent_frame, text="第三步: 选择具体手段", font=("微软雅黑", 11), bg="white")
    opponent_step3_frame.pack(fill="x", padx=10, pady=5)
    tk.Label(opponent_step3_frame, text="对策和反制选项...", font=("微软雅黑", 10), bg="white", fg="gray").pack(pady=10)
    
    # 主角模块
    protagonist_frame = tk.LabelFrame(modules_pane, text="主角模块", font=("微软雅黑", 14, "bold"), bg="white")
    modules_pane.add(protagonist_frame, minsize=600)
    
    # 主角模块 - 第一步：选择具体主题
    protagonist_theme_frame = tk.LabelFrame(
        protagonist_frame, 
        text="第一步: 选择具体主题 (7个选项全展示)", 
        font=("微软雅黑", 12, "bold"),
        bg="white",
        fg="#3498DB"
    )
    protagonist_theme_frame.pack(fill="x", padx=10, pady=10)
    
    # 主角主题选择网格
    protagonist_grid_frame = tk.Frame(protagonist_theme_frame, bg="white")
    protagonist_grid_frame.pack(fill="x", padx=10, pady=10)
    
    for i, theme in enumerate(themes):
        row = i // 4  # 每行4个，共2行
        col = i % 4
        
        rb = tk.Radiobutton(
            protagonist_grid_frame,
            text=theme,
            variable=protagonist_theme_var,
            value=theme,
            command=update_status,
            font=("微软雅黑", 11),
            anchor="w",
            bg="white",
            activebackground="#E3F2FD"
        )
        rb.grid(row=row, column=col, sticky="w", padx=8, pady=5)
    
    # 主角模块 - 第二步和第三步的占位符
    protagonist_step2_frame = tk.LabelFrame(protagonist_frame, text="第二步: 选择具体境况", font=("微软雅黑", 11), bg="white")
    protagonist_step2_frame.pack(fill="x", padx=10, pady=5)
    tk.Label(protagonist_step2_frame, text="等待选择主题后显示境况选项...", font=("微软雅黑", 10), bg="white", fg="gray").pack(pady=10)
    
    protagonist_step3_frame = tk.LabelFrame(protagonist_frame, text="第三步: 选择具体手段", font=("微软雅黑", 11), bg="white")
    protagonist_step3_frame.pack(fill="x", padx=10, pady=5)
    tk.Label(protagonist_step3_frame, text="对策和反制选项...", font=("微软雅黑", 10), bg="white", fg="gray").pack(pady=10)
    
    # 底部状态显示
    status_frame = tk.LabelFrame(main_frame, text="当前选择状态", font=("微软雅黑", 12, "bold"), bg="white")
    status_frame.pack(fill="x", pady=10)
    
    status_text = tk.Text(status_frame, height=4, font=("微软雅黑", 11), bg="#F8F9FA")
    status_text.pack(fill="x", padx=10, pady=10)
    
    # 设置默认选择
    if themes:
        opponent_theme_var.set(themes[0])
        protagonist_theme_var.set(themes[0])
    
    update_status()
    
    # 底部信息
    info_frame = tk.Frame(main_frame, bg="white")
    info_frame.pack(fill="x", pady=5)
    
    info_label = tk.Label(
        info_frame,
        text=f"✅ 每个模块第一步都显示 {len(themes)} 个主题选项，按 2×4 网格排列",
        font=("微软雅黑", 12, "bold"),
        bg="white",
        fg="#27AE60"
    )
    info_label.pack()
    
    print("✅ 主题选择全展示测试界面创建完成！")
    print("📝 特点:")
    print(f"   - 对手模块第一步: {len(themes)} 个主题选项全展示")
    print(f"   - 主角模块第一步: {len(themes)} 个主题选项全展示")
    print("   - 按 2×4 网格排列（7个选项，第二行3个）")
    print("   - 实时显示当前选择状态")
    
    root.mainloop()

if __name__ == "__main__":
    test_theme_selection()
