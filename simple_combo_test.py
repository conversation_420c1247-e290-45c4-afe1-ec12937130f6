# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

def main():
    root = tk.Tk()
    root.title("简单组合框测试")
    root.geometry("800x300")
    
    # 生成组合选项
    themes = ["生命力量", "精神力量", "思维力量", "物质资源", "信息资源", "关系资源", "测试项"]
    combinations = []
    for o_theme in themes:
        for p_theme in themes:
            combinations.append(f"对手{o_theme} — 主角{p_theme}")
    
    print(f"生成了 {len(combinations)} 个组合选项")
    
    frame = ttk.Frame(root, padding=20)
    frame.pack(fill="both", expand=True)
    
    # 标题
    ttk.Label(frame, text="组合框测试 - 应该显示49个选项", font=("微软雅黑", 12, "bold")).pack(pady=10)
    
    # 组合框
    combo_var = tk.StringVar()
    combo = ttk.Combobox(
        frame,
        textvariable=combo_var,
        values=tuple(combinations),
        state="readonly",
        width=60,
        font=("微软雅黑", 10)
    )
    combo.pack(pady=10)
    
    # 设置默认值
    if combinations:
        combo_var.set(combinations[0])
    
    # 信息显示
    info_label = ttk.Label(frame, text="", font=("微软雅黑", 10))
    info_label.pack(pady=10)
    
    def on_select(event=None):
        selected = combo_var.get()
        try:
            index = combinations.index(selected) + 1
            info_label.config(text=f"选择了第 {index} 个选项: {selected}")
        except ValueError:
            info_label.config(text=f"选择了: {selected}")
    
    combo.bind("<<ComboboxSelected>>", on_select)
    
    # 验证按钮
    def verify():
        values = combo['values']
        result = f"组合框包含 {len(values)} 个选项（应该是49个）"
        if len(values) == 49:
            result += " ✓ 正确！"
        else:
            result += " ✗ 错误！"
        print(result)
        
        # 显示前5个和后5个选项
        print("前5个选项:")
        for i in range(min(5, len(values))):
            print(f"  {i+1}: {values[i]}")
        
        if len(values) > 10:
            print("...")
            print("后5个选项:")
            for i in range(max(0, len(values)-5), len(values)):
                print(f"  {i+1}: {values[i]}")
    
    ttk.Button(frame, text="验证选项数量", command=verify).pack(pady=10)
    
    # 说明
    ttk.Label(frame, text="请点击下拉箭头查看是否显示所有49个选项", 
              font=("微软雅黑", 10), foreground="blue").pack(pady=5)
    
    # 初始显示
    on_select()
    
    print("程序启动完成，请点击下拉框查看选项")
    root.mainloop()

if __name__ == "__main__":
    main()
