# -*- coding: utf-8 -*-

def test_tactics_direct():
    """直接测试对策反制逻辑"""
    
    print("🔍 直接测试对策反制逻辑")
    print("=" * 50)
    
    # 模拟程序中的theme_tactics字典
    theme_tactics = {
        "生命受威胁": {
            "对策": ["金蝉脱壳", "躲入暗处", "伪造身份", "寻求公义", "假死脱身", "投靠强敌", "鱼死网破", "绑架人质"],
            "反制": ["买凶杀人", "天罗地网", "识破伪装", "官官相护", "识破诡计", "借刀杀人", "早有准备", "拒不受胁"]
        },
        "安全有隐患": {
            "对策": ["深居简出", "暗中调查", "设置陷阱", "寻求庇护", "利益交换", "雇佣保镖", "先发制人", "制造伪证"],
            "反制": ["暗中布局", "加紧监视", "隐藏更深", "釜底抽薪", "出尔反尔", "收买保镖", "早有准备", "找出破绽"]
        }
    }
    
    # 通用对策反制
    action_types = [
        "自主-(好)", "互动-(好)", "强制-(好)", "运气-(好)",
        "自主-(坏)", "互动-(坏)", "强制-(坏)", "运气-(坏)"
    ]
    
    # 测试不同主题
    test_themes = ["生命受威胁", "安全有隐患", "不存在的主题"]
    
    for theme_name in test_themes:
        print(f"\n🎯 测试主题: '{theme_name}'")
        
        # 模拟程序逻辑
        if theme_name in theme_tactics:
            tactics = theme_tactics[theme_name]["对策"]
            counters = theme_tactics[theme_name]["反制"]
            print(f"✅ 使用主题专用对策反制")
        else:
            tactics = action_types
            counters = action_types
            print(f"❌ 主题未找到，使用通用对策反制")
        
        print(f"  对策: {tactics}")
        print(f"  反制: {counters}")
    
    print("\n" + "=" * 50)
    print("🎯 测试完成")

if __name__ == "__main__":
    test_tactics_direct()
