# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import json

def test_all_modules():
    """测试所有模块的全展示效果"""
    
    root = tk.Tk()
    root.title("所有模块全展示测试")
    root.geometry("1600x1000")
    
    print("🎯 测试所有模块全展示效果")
    print("=" * 60)
    
    # 数据定义
    themes = ["生命力量", "精神力量", "思维力量", "物质资源", "信息资源", "关系资源", "测试项"]
    
    strategic_stances = [
        "对手 机遇 / 主角 机遇 (强强对抗)",
        "对手 危机 / 主角 机遇 (主角优势)",
        "对手 机遇 / 主角 危机 (主角劣势)",
        "对手 危机 / 主角 危机 (泥潭互搏)"
    ]
    
    tactical_stances = [
        "极限换伤 (主角进攻 vs 对手进攻)",
        "攻防大战 (主角进攻 vs 对手防守)",
        "生死时速 (主角防守 vs 对手进攻)",
        "各自为战 (主角防守 vs 对手防守)"
    ]
    
    action_types = [
        "自主-(好)", "互动-(好)", "强制-(好)", "运气-(好)",
        "自主-(坏)", "互动-(坏)", "强制-(坏)", "运气-(坏)"
    ]
    
    # 生成组合选项
    combinations = []
    for o_theme in themes:
        for p_theme in themes:
            combinations.append(f"对手{o_theme} — 主角{p_theme}")
    
    # 加载测试境况数据
    test_situations = {}
    try:
        with open("测试.json", 'r', encoding='utf-8') as f:
            test_data = json.load(f)
            test_situations = test_data
    except Exception as e:
        print(f"加载测试.json失败: {e}")
    
    print(f"📊 数据统计:")
    print(f"   - 组合选项: {len(combinations)} 个")
    print(f"   - 战略态势: {len(strategic_stances)} 个")
    print(f"   - 战术姿态: {len(tactical_stances)} 个")
    print(f"   - 行动类型: {len(action_types)} 个")
    print(f"   - 测试境况: {len(test_situations.get('机遇', []))} 个机遇 + {len(test_situations.get('危机', []))} 个危机")
    print("=" * 60)
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🎯 完整的五模块全展示测试", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=10)
    
    # 创建主要的水平分割
    main_pane = tk.PanedWindow(main_frame, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=8)
    main_pane.pack(fill="both", expand=True)
    
    # 左侧：组合模块
    left_frame = tk.Frame(main_pane)
    main_pane.add(left_frame, width=500, minsize=400)
    
    combo_label = tk.Label(left_frame, text="组合模块 - 三步全展示", font=("微软雅黑", 14, "bold"))
    combo_label.pack(pady=10)
    
    # 选择变量
    combination_var = tk.StringVar()
    strategic_var = tk.StringVar()
    tactical_var = tk.StringVar()
    
    # 第一步：组合能量（简化显示前20个）
    step1_frame = tk.LabelFrame(left_frame, text="第一步: 组合能量 (显示前20个)", font=("微软雅黑", 11, "bold"))
    step1_frame.pack(fill="x", padx=5, pady=5)
    
    combo_grid = tk.Frame(step1_frame)
    combo_grid.pack(fill="x", padx=5, pady=5)
    
    for i, combo in enumerate(combinations[:20]):  # 只显示前20个以节省空间
        row = i // 4
        col = i % 4
        rb = tk.Radiobutton(combo_grid, text=combo, variable=combination_var, value=combo, font=("微软雅黑", 8), anchor="w")
        rb.grid(row=row, column=col, sticky="w", padx=2, pady=1)
    
    # 第二步：战略态势
    step2_frame = tk.LabelFrame(left_frame, text="第二步: 战略态势", font=("微软雅黑", 11, "bold"))
    step2_frame.pack(fill="x", padx=5, pady=5)
    
    strategic_grid = tk.Frame(step2_frame)
    strategic_grid.pack(fill="x", padx=5, pady=5)
    
    for i, stance in enumerate(strategic_stances):
        rb = tk.Radiobutton(strategic_grid, text=stance, variable=strategic_var, value=stance, font=("微软雅黑", 9), anchor="w", wraplength=200)
        rb.grid(row=i//2, column=i%2, sticky="w", padx=5, pady=2)
    
    # 第三步：战术姿态
    step3_frame = tk.LabelFrame(left_frame, text="第三步: 战术姿态", font=("微软雅黑", 11, "bold"))
    step3_frame.pack(fill="x", padx=5, pady=5)
    
    tactical_grid = tk.Frame(step3_frame)
    tactical_grid.pack(fill="x", padx=5, pady=5)
    
    for i, stance in enumerate(tactical_stances):
        rb = tk.Radiobutton(tactical_grid, text=stance, variable=tactical_var, value=stance, font=("微软雅黑", 9), anchor="w", wraplength=200)
        rb.grid(row=i//2, column=i%2, sticky="w", padx=5, pady=2)
    
    # 右侧：对手和主角模块
    right_frame = tk.Frame(main_pane)
    main_pane.add(right_frame, minsize=800)
    
    modules_label = tk.Label(right_frame, text="对手模块 & 主角模块 - 三步全展示", font=("微软雅黑", 14, "bold"))
    modules_label.pack(pady=10)
    
    # 创建对手和主角模块的水平分割
    modules_pane = tk.PanedWindow(right_frame, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, sashwidth=5)
    modules_pane.pack(fill="both", expand=True)
    
    # 对手模块
    opponent_frame = tk.LabelFrame(modules_pane, text="对手模块", font=("微软雅黑", 12, "bold"))
    modules_pane.add(opponent_frame, minsize=350)
    
    # 主角模块
    protagonist_frame = tk.LabelFrame(modules_pane, text="主角模块", font=("微软雅黑", 12, "bold"))
    modules_pane.add(protagonist_frame, minsize=350)
    
    # 为对手和主角模块创建相同的结构
    for module_frame, module_name in [(opponent_frame, "对手"), (protagonist_frame, "主角")]:
        
        # 第一步：当前主题
        theme_frame = tk.LabelFrame(module_frame, text="第一步: 当前主题", font=("微软雅黑", 10))
        theme_frame.pack(fill="x", padx=5, pady=3)
        theme_label = tk.Label(theme_frame, text="等待选择组合...", font=("微软雅黑", 9))
        theme_label.pack(anchor="w", padx=5)
        
        # 第二步：具体境况
        situation_frame = tk.LabelFrame(module_frame, text="第二步: 具体境况", font=("微软雅黑", 10))
        situation_frame.pack(fill="x", padx=5, pady=3)
        
        # 显示测试境况数据
        if test_situations:
            situations_canvas = tk.Canvas(situation_frame, height=120)
            situations_scrollbar = tk.Scrollbar(situation_frame, orient="vertical", command=situations_canvas.yview)
            situations_scrollable = tk.Frame(situations_canvas)
            
            situations_scrollable.bind("<Configure>", lambda e: situations_canvas.configure(scrollregion=situations_canvas.bbox("all")))
            situations_canvas.create_window((0, 0), window=situations_scrollable, anchor="nw")
            situations_canvas.configure(yscrollcommand=situations_scrollbar.set)
            
            # 显示机遇境况（前5个）
            for i, item in enumerate(test_situations.get('机遇', [])[:5]):
                name = item.get("name", "")
                item_frame = tk.Frame(situations_scrollable)
                item_frame.pack(fill="x", padx=2, pady=1)
                tk.Label(item_frame, text=f"{name}:", font=("微软雅黑", 8)).pack(side="left")
                for level, text in item.get("levels", {}).items():
                    tk.Radiobutton(item_frame, text=f"{level}", font=("微软雅黑", 7)).pack(side="left", padx=2)
            
            situations_canvas.pack(side="left", fill="both", expand=True)
            situations_scrollbar.pack(side="right", fill="y")
        
        # 第三步：具体手段
        action_frame = tk.LabelFrame(module_frame, text="第三步: 具体手段", font=("微软雅黑", 10))
        action_frame.pack(fill="x", padx=5, pady=3)
        
        # 对策选择
        tactic_subframe = tk.LabelFrame(action_frame, text="对策", font=("微软雅黑", 9))
        tactic_subframe.pack(fill="x", padx=2, pady=2)
        
        tactic_grid = tk.Frame(tactic_subframe)
        tactic_grid.pack(fill="x", padx=2, pady=2)
        
        for i, atype in enumerate(action_types):
            row = i // 4
            col = i % 4
            rb = tk.Radiobutton(tactic_grid, text=atype, font=("微软雅黑", 7), anchor="w")
            rb.grid(row=row, column=col, sticky="w", padx=1, pady=1)
        
        # 反制选择
        counter_subframe = tk.LabelFrame(action_frame, text="反制", font=("微软雅黑", 9))
        counter_subframe.pack(fill="x", padx=2, pady=2)
        
        counter_grid = tk.Frame(counter_subframe)
        counter_grid.pack(fill="x", padx=2, pady=2)
        
        for i, atype in enumerate(action_types):
            row = i // 4
            col = i % 4
            rb = tk.Radiobutton(counter_grid, text=atype, font=("微软雅黑", 7), anchor="w")
            rb.grid(row=row, column=col, sticky="w", padx=1, pady=1)
    
    # 设置默认选择
    if combinations:
        combination_var.set(combinations[0])
    if strategic_stances:
        strategic_var.set(strategic_stances[0])
    if tactical_stances:
        tactical_var.set(tactical_stances[0])
    
    print("✅ 所有模块全展示测试界面创建完成！")
    print("📝 包含的模块:")
    print("   1. 组合模块 - 三步全展示")
    print("   2. 对手模块 - 三步全展示")
    print("   3. 主角模块 - 三步全展示")
    print("🎉 总计五个模块，所有选项全部展示！")
    
    root.mainloop()

if __name__ == "__main__":
    test_all_modules()
