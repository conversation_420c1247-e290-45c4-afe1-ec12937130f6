# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import messagebox
import json
import os
import datetime

class NovelOutlineApp:
    """
    一个用于整理和生成小说细纲的GUI应用程序。
    v7.0: 调整为单栏垂直布局，优化操作流程。
    """
    def __init__(self, root):
        self.root = root
        self.root.title("小说细纲生成工具 v7.0 (单栏布局版)")
        self.root.geometry("800x900") # 调整了默认窗口尺寸以适应新布局
        
        self.manual_save_dir = "手动保存"
        if not os.path.exists(self.manual_save_dir):
            os.makedirs(self.manual_save_dir)
            
        self.relation_layout = [
            ["亲属", "配偶", "秘书", "恩人"],
            ["人才", "商人", "记者", "退干"],
            ["对手", "上级", "纪委", "群众"],
            ["下属", "同盟", "靠山", "线人"]
        ]
        
        self.selected_relation = None
        self.selected_plot = None
        self.selected_method = None
        
        self.current_relation_data = {}
        
        self.relation_buttons = {}
        self.plot_buttons = {}
        self.method_buttons = {}
        
        self._create_widgets()
        
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def _load_relation_data(self, relation_name):
        filepath = f"{relation_name}.json"
        if not os.path.exists(filepath):
            messagebox.showerror("数据文件缺失", f"找不到文件: {filepath}\n请确保所有JSON文件都在程序目录下。")
            return {}
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            messagebox.showerror("错误", f"加载数据文件 '{filepath}' 失败: {e}")
            return {}

    # --- MODIFIED: 此函数已重写以实现单栏垂直布局 ---
    def _create_widgets(self):
        """创建所有界面组件"""
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # === 顶部：主要功能按钮 ===
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill="x", padx=5, pady=5, anchor="n")
        
        tk.Button(buttons_frame, text="生成细纲", command=self.generate_outline, 
                 bg="#4CAF50", fg="white", font=("微软雅黑", 12, "bold"), padx=20, pady=5).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="清空输入", command=self.clear_inputs, 
                 font=("微软雅黑", 12), padx=20, pady=5).pack(side=tk.LEFT, padx=5)
        
        # === 关系选择区域 ===
        relation_frame = tk.LabelFrame(main_frame, text="第一步：选择关系", font=("微软雅黑", 11))
        relation_frame.pack(fill="x", padx=5, pady=5, anchor="n")
        
        for row_index, row_list in enumerate(self.relation_layout):
            for col_index, cat in enumerate(row_list):
                var = tk.IntVar()
                btn = tk.Checkbutton(relation_frame, text=cat, variable=var, 
                                   font=("微软雅黑", 10), command=lambda c=cat: self.on_relation_clicked(c))
                btn.grid(row=row_index, column=col_index, padx=10, pady=2, sticky="w")
        
        # === 情节选择区域 (动态生成) ===
        self.plot_frame_container = tk.LabelFrame(main_frame, text="第二步：选择情节", font=("微软雅黑", 11))
        self.plot_frame_container.pack(fill="x", padx=5, pady=5, anchor="n")
        
        # === 手段选择区域 (动态生成) ===
        self.method_frame_container = tk.LabelFrame(main_frame, text="第三步：选择手段", font=("微软雅黑", 11))
        self.method_frame_container.pack(fill="x", padx=5, pady=5, anchor="n")
        
        # === 创作区 (现在放在最下面) ===
        composition_frame = tk.LabelFrame(main_frame, text="创作区", font=("微软雅黑", 11))
        composition_frame.pack(fill="both", expand=True, padx=5, pady=10)
        
        tools_frame = tk.Frame(composition_frame)
        tools_frame.pack(fill="x", padx=5, pady=5)
        
        tk.Button(tools_frame, text="保存创作", command=self.save_composition, 
                 bg="#AED581", font=("微软雅黑", 10), padx=10).pack(side=tk.LEFT, padx=5)
        
        tk.Button(tools_frame, text="清空创作区", command=self.clear_composition, 
                 font=("微软雅黑", 10), padx=10).pack(side=tk.LEFT, padx=5)
        
        tk.Button(tools_frame, text="复制内容", command=self.copy_composition, 
                 font=("微软雅黑", 10), padx=10).pack(side=tk.LEFT, padx=5)
        
        scrollbar = tk.Scrollbar(composition_frame)
        scrollbar.pack(side=tk.RIGHT, fill="y")
        
        self.composition_area = tk.Text(composition_frame, relief="solid", borderwidth=1, 
                                      wrap="word", font=("微软雅黑", 11), 
                                      yscrollcommand=scrollbar.set)
        self.composition_area.pack(fill="both", expand=True)
        scrollbar.config(command=self.composition_area.yview)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = tk.Label(self.root, textvariable=self.status_var, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def on_relation_clicked(self, category):
        for cat, data in self.relation_buttons.items():
            if cat != category and data["var"].get() == 1:
                data["var"].set(0)
        
        if self.relation_buttons[category]["var"].get() == 1:
            self.selected_relation = category
            self.current_relation_data = self._load_relation_data(category)
            self.status_var.set(f"已选择关系: {category}")
            self._update_plot_ui()
            self._update_method_ui(None)
            self.selected_plot = None
            self.selected_method = None
        else:
            self.selected_relation = None
            self.current_relation_data = {}
            self.status_var.set("未选择关系")
            self._update_plot_ui()
            self._update_method_ui(None)

    def _update_plot_ui(self):
        for widget in self.plot_frame_container.winfo_children():
            widget.destroy()
        
        self.plot_buttons.clear()
        
        plot_cols = 4

        good_plots_data = self.current_relation_data.get("好情节", {})
        if good_plots_data:
            good_frame = tk.LabelFrame(self.plot_frame_container, text="好情节", font=("微软雅黑", 10), fg="green")
            good_frame.pack(fill="x", padx=5, pady=5)
            for i, plot in enumerate(good_plots_data.keys()):
                row, col = divmod(i, plot_cols)
                var = tk.IntVar()
                btn = tk.Checkbutton(good_frame, text=plot, variable=var, 
                                   font=("微软雅黑", 10), command=lambda p=plot: self.on_plot_clicked(p))
                btn.grid(row=row, column=col, padx=10, pady=2, sticky="w")
                self.plot_buttons[plot] = {"button": btn, "var": var}

        bad_plots_data = self.current_relation_data.get("坏情节", {})
        if bad_plots_data:
            bad_frame = tk.LabelFrame(self.plot_frame_container, text="坏情节", font=("微软雅黑", 10), fg="red")
            bad_frame.pack(fill="x", padx=5, pady=5)
            for i, plot in enumerate(bad_plots_data.keys()):
                row, col = divmod(i, plot_cols)
                var = tk.IntVar()
                btn = tk.Checkbutton(bad_frame, text=plot, variable=var, 
                                   font=("微软雅黑", 10), command=lambda p=plot: self.on_plot_clicked(p))
                btn.grid(row=row, column=col, padx=10, pady=2, sticky="w")
                self.plot_buttons[plot] = {"button": btn, "var": var}

    def on_plot_clicked(self, plot):
        for p, data in self.plot_buttons.items():
            if p != plot and data["var"].get() == 1:
                data["var"].set(0)

        if self.plot_buttons[plot]["var"].get() == 1:
            self.selected_plot = plot
            self.status_var.set(f"已选择情节: {plot}")
            self._update_method_ui(plot)
            self.selected_method = None
        else:
            self.selected_plot = None
            self.status_var.set("未选择情节")
            self._update_method_ui(None)
    
    def _update_method_ui(self, plot):
        for widget in self.method_frame_container.winfo_children():
            widget.destroy()

        self.method_buttons.clear()
        if not plot:
            return
        
        methods_data = self.current_relation_data.get("好情节", {}).get(plot)
        if not methods_data:
            methods_data = self.current_relation_data.get("坏情节", {}).get(plot, {})
        
        method_cols = 4

        if methods_data.get("好手段"):
            good_frame = tk.LabelFrame(self.method_frame_container, text="好手段", font=("微软雅黑", 10), fg="green")
            good_frame.pack(fill="x", padx=5, pady=5)
            for i, method in enumerate(methods_data["好手段"]):
                row, col = divmod(i, method_cols)
                var = tk.IntVar()
                btn = tk.Checkbutton(good_frame, text=method, variable=var, font=("微软雅黑", 10), command=lambda m=method: self.on_method_clicked(m))
                btn.grid(row=row, column=col, padx=10, pady=2, sticky="w")
                self.method_buttons[method] = {"button": btn, "var": var}

        if methods_data.get("坏手段"):
            bad_frame = tk.LabelFrame(self.method_frame_container, text="坏手段", font=("微软雅黑", 10), fg="red")
            bad_frame.pack(fill="x", padx=5, pady=5)
            for i, method in enumerate(methods_data["坏手段"]):
                row, col = divmod(i, method_cols)
                var = tk.IntVar()
                btn = tk.Checkbutton(bad_frame, text=method, variable=var, font=("微软雅黑", 10), command=lambda m=method: self.on_method_clicked(m))
                btn.grid(row=row, column=col, padx=10, pady=2, sticky="w")
                self.method_buttons[method] = {"button": btn, "var": var}

    def on_method_clicked(self, method):
        for m, data in self.method_buttons.items():
            if m != method and data["var"].get() == 1:
                data["var"].set(0)
        
        if self.method_buttons[method]["var"].get() == 1:
            self.selected_method = method
            self.status_var.set(f"已选择手段: {method}")
        else:
            if self.selected_method == method:
                self.selected_method = None
                self.status_var.set("未选择手段")

    def generate_outline(self):
        if not self.selected_relation or not self.selected_plot or not self.selected_method:
            messagebox.showwarning("输入不完整", "请完整选择“关系”、“情节”和“手段”!")
            return
        
        outline = f"{self.selected_relation}（） + {self.selected_plot}；（） {self.selected_method}"
        
        current_text = self.composition_area.get('1.0', tk.END).strip()
        if current_text:
            self.composition_area.insert(tk.END, "\n" + outline)
        else:
            self.composition_area.insert(tk.END, outline)
        
        self.status_var.set(f"已生成: {outline}")

    def clear_inputs(self):
        for data in self.relation_buttons.values():
            data["var"].set(0)
        
        self.current_relation_data = {}
        self._update_plot_ui()
        self._update_method_ui(None)
        
        self.selected_relation = None
        self.selected_plot = None
        self.selected_method = None
        self.status_var.set("已清空所有选择")

    def clear_composition(self):
        self.composition_area.delete('1.0', tk.END)
        self.status_var.set("已清空创作区")

    def copy_composition(self):
        content = self.composition_area.get('1.0', tk.END).strip()
        if content:
            self.root.clipboard_clear()
            self.root.clipboard_append(content)
            self.status_var.set("已复制创作区内容到剪贴板")
            messagebox.showinfo("已复制", "创作区内容已成功复制到剪贴板！")
        else:
            messagebox.showwarning("内容为空", "创作区没有内容可复制。")

    def save_composition(self):
        content = self.composition_area.get("1.0", tk.END).strip()
        if not content:
            self.status_var.set("创作区为空，不执行保存")
            return
        
        now = datetime.datetime.now()
        filename = now.strftime("%Y-%m-%d_%H-%M-%S") + ".txt"
        filepath = os.path.join(self.manual_save_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            self.status_var.set(f"内容已保存到: {filepath}")
            messagebox.showinfo("保存成功", f"内容已保存到:\n{filepath}")
        except Exception as e:
            self.status_var.set(f"保存失败: {e}")
            messagebox.showerror("保存失败", f"保存到文件失败: {e}")

    def on_closing(self):
        if messagebox.askokcancel("退出", "确定要退出吗？"):
            self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = NovelOutlineApp(root)
    root.mainloop()