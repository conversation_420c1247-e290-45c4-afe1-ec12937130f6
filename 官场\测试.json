{"situations": {"机遇": ["机遇A", "机遇B", "机遇C"], "危机": ["危机A", "危机B", "危机C"]}, "action_archetypes": {"自主(好)": ["自主行动(好)A", "自主行动(好)B"], "自主(坏)": ["自主行动(坏)A", "自主行动(坏)B"], "互动(好)": ["互动行动(好)A", "互动行动(好)B"], "互动(坏)": ["互动行动(坏)A", "互动行动(坏)B"], "强制(好)": ["强制行动(好)A", "强制行动(好)B"], "强制(坏)": ["强制行动(坏)A", "强制行动(坏)B"], "运气(好)": ["运气事件(好)A", "运气事件(好)B"], "运气(坏)": ["运气事件(坏)A", "运气事件(坏)B"]}, "result_matrix": {"主角机遇_对手机遇": {"自主(好)_vs_自主(好)": "代价", "自主(好)_vs_自主(坏)": "成功", "自主(坏)_vs_强制(好)": "失败", "运气(好)_vs_运气(坏)": "意外"}, "主角机遇_对手危机": {"自主(好)_vs_自主(好)": "成功", "自主(好)_vs_自主(坏)": "成功", "自主(坏)_vs_强制(好)": "意外", "运气(好)_vs_运气(坏)": "意外"}, "主角危机_对手机遇": {"自主(好)_vs_自主(好)": "失败", "自主(好)_vs_自主(坏)": "代价", "自主(坏)_vs_强制(好)": "失败", "运气(好)_vs_运气(坏)": "意外"}, "主角危机_对手危机": {"自主(好)_vs_自主(好)": "意外", "自主(好)_vs_自主(坏)": "成功", "自主(坏)_vs_强制(好)": "代价", "运气(好)_vs_运气(坏)": "意外"}}}