# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

def test_tactics_fix():
    """测试对策反制修复"""
    
    root = tk.Tk()
    root.title("对策反制修复测试")
    root.geometry("800x600")
    
    print("🔍 测试对策反制修复")
    print("=" * 50)
    
    # 工作主题专用对策和反制
    work_tactics = {
        "对策": [
            "庆祝升职：升职后举办庆祝活动，巩固地位",
            "拉拢同事：主动与同事建立良好关系",
            "展示能力：在重要场合展现工作能力",
            "寻求机会：主动寻找更好的工作机会"
        ],
        "反制": [
            "使绊子：对升职的同事进行暗中阻挠",
            "散布谣言：传播不利于对手的消息",
            "抢夺功劳：将他人的工作成果据为己有",
            "孤立对手：联合他人排斥特定同事"
        ]
    }
    
    # 主框架
    main_frame = tk.Frame(root, bg="white")
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 标题
    title_label = tk.Label(
        main_frame, 
        text="🔧 对策反制修复测试", 
        font=("微软雅黑", 16, "bold"),
        bg="white",
        fg="#2E86AB"
    )
    title_label.pack(pady=10)
    
    # 测试tk.Radiobutton（应该正常工作）
    tk_frame = tk.LabelFrame(main_frame, text="✅ tk.Radiobutton 测试（支持font）", font=("微软雅黑", 12, "bold"))
    tk_frame.pack(fill="x", padx=10, pady=10)
    
    tk_var = tk.StringVar()
    
    for i, tactic in enumerate(work_tactics["对策"]):
        rb = tk.Radiobutton(
            tk_frame,
            text=tactic,
            variable=tk_var,
            value=tactic,
            font=("微软雅黑", 10),
            anchor="w",
            wraplength=300,
            justify="left"
        )
        rb.grid(row=i//2, column=i%2, sticky="w", padx=5, pady=3)
    
    if work_tactics["对策"]:
        tk_var.set(work_tactics["对策"][0])
    
    # 测试ttk.Radiobutton（不支持font）
    ttk_frame = tk.LabelFrame(main_frame, text="⚠️ ttk.Radiobutton 测试（不支持font）", font=("微软雅黑", 12, "bold"))
    ttk_frame.pack(fill="x", padx=10, pady=10)
    
    ttk_var = tk.StringVar()
    
    for i, counter in enumerate(work_tactics["反制"]):
        rb = ttk.Radiobutton(
            ttk_frame,
            text=counter,
            variable=ttk_var,
            value=counter
            # 注意：这里不能使用font参数
        )
        rb.grid(row=i//2, column=i%2, sticky="w", padx=5, pady=3)
    
    if work_tactics["反制"]:
        ttk_var.set(work_tactics["反制"][0])
    
    # 当前选择显示
    selection_frame = tk.Frame(main_frame, bg="white")
    selection_frame.pack(fill="x", padx=10, pady=10)
    
    tk.Label(selection_frame, text="当前选择:", font=("微软雅黑", 12, "bold"), bg="white").pack(anchor="w")
    
    tk_selection_label = tk.Label(selection_frame, text="", font=("微软雅黑", 10), bg="white", fg="#27AE60")
    tk_selection_label.pack(anchor="w", padx=10)
    
    ttk_selection_label = tk.Label(selection_frame, text="", font=("微软雅黑", 10), bg="white", fg="#E74C3C")
    ttk_selection_label.pack(anchor="w", padx=10)
    
    def update_selections():
        tk_selection_label.config(text=f"tk选择: {tk_var.get()}")
        ttk_selection_label.config(text=f"ttk选择: {ttk_var.get()}")
        print(f"tk选择: {tk_var.get()}")
        print(f"ttk选择: {ttk_var.get()}")
    
    tk_var.trace('w', lambda *args: update_selections())
    ttk_var.trace('w', lambda *args: update_selections())
    
    # 说明文本
    info_frame = tk.Frame(main_frame, bg="white")
    info_frame.pack(fill="x", padx=10, pady=10)
    
    info_text = """
🔧 修复说明:
• tk.Radiobutton 支持 font 参数，可以设置字体
• ttk.Radiobutton 不支持 font 参数，使用会报错
• 修复方案：在 _update_tactics 方法中使用 tk.Radiobutton 替代 ttk.Radiobutton

✅ 修复后的效果:
• 工作主题的对策反制可以正常显示
• 支持自定义字体和文本换行
• 不会再出现 "unknown option '-font'" 错误
    """
    
    info_label = tk.Label(
        info_frame,
        text=info_text,
        font=("微软雅黑", 10),
        bg="white",
        fg="#333333",
        justify="left"
    )
    info_label.pack(anchor="w")
    
    print("✅ 修复测试界面创建完成")
    print("📝 tk.Radiobutton 支持font参数，ttk.Radiobutton 不支持")
    print("🔧 已将 _update_tactics 方法中的 ttk.Radiobutton 改为 tk.Radiobutton")
    
    # 自动更新显示
    root.after(100, update_selections)
    
    root.mainloop()

if __name__ == "__main__":
    test_tactics_fix()
