# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

def test_dropdown_display():
    """验证下拉框是否显示所有49个选项"""
    
    root = tk.Tk()
    root.title("验证下拉框显示")
    root.geometry("600x400")
    
    # 定义主题数据
    themes = [
        "生命力量", "精神力量", "思维力量", 
        "物质资源", "信息资源", "关系资源", "测试项"
    ]
    
    # 生成所有组合
    combinations = []
    for o_theme in themes:
        for p_theme in themes:
            combinations.append(f"对手{o_theme} — 主角{p_theme}")
    
    print(f"总共生成了 {len(combinations)} 个组合选项")
    
    # 创建界面
    main_frame = ttk.Frame(root, padding=20)
    main_frame.pack(fill="both", expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="组合模块下拉框测试", font=("微软雅黑", 14, "bold"))
    title_label.pack(pady=10)
    
    # 说明
    info_label = ttk.Label(main_frame, text="请点击下拉框查看是否显示所有49个选项：", font=("微软雅黑", 10))
    info_label.pack(pady=5)
    
    # 组合选择器
    combo_frame = ttk.Frame(main_frame)
    combo_frame.pack(fill="x", pady=10)
    
    ttk.Label(combo_frame, text="选择组合能量:", font=("微软雅黑", 10)).pack(side="left", padx=5)
    
    combination_var = tk.StringVar()
    combination_combo = ttk.Combobox(
        combo_frame, 
        textvariable=combination_var, 
        values=tuple(combinations),
        state="readonly", 
        width=50,
        font=("微软雅黑", 9)
    )
    combination_combo.pack(side="left", fill="x", expand=True, padx=5)
    
    # 设置默认值
    if combinations:
        combination_var.set(combinations[0])
    
    # 当前选择显示
    current_label = ttk.Label(main_frame, text="", font=("微软雅黑", 10), foreground="blue")
    current_label.pack(pady=10)
    
    def on_selection_change(event=None):
        selected = combination_var.get()
        current_label.config(text=f"当前选择: {selected}")
        
        # 找到选择的索引
        try:
            index = combinations.index(selected)
            print(f"选择了第 {index + 1} 个选项: {selected}")
        except ValueError:
            print(f"选择了: {selected}")
    
    combination_combo.bind("<<ComboboxSelected>>", on_selection_change)
    
    # 验证按钮
    def verify_options():
        """验证下拉框中的选项数量"""
        values = combination_combo['values']
        print(f"\n=== 下拉框验证结果 ===")
        print(f"下拉框中的选项数量: {len(values)}")
        print(f"预期选项数量: {len(combinations)}")
        print(f"选项数量匹配: {'✓' if len(values) == len(combinations) else '✗'}")
        
        if len(values) == len(combinations):
            result_text = f"✓ 验证成功！下拉框包含所有 {len(combinations)} 个选项"
            result_label.config(text=result_text, foreground="green")
        else:
            result_text = f"✗ 验证失败！下拉框只有 {len(values)} 个选项，应该有 {len(combinations)} 个"
            result_label.config(text=result_text, foreground="red")
        
        # 显示前10个选项作为示例
        print(f"\n前10个选项:")
        for i, option in enumerate(values[:10]):
            print(f"  {i+1:2d}: {option}")
        
        if len(values) > 10:
            print(f"  ... 还有 {len(values) - 10} 个选项")
    
    verify_button = ttk.Button(main_frame, text="验证选项数量", command=verify_options)
    verify_button.pack(pady=10)
    
    # 结果显示
    result_label = ttk.Label(main_frame, text="点击'验证选项数量'按钮查看结果", font=("微软雅黑", 10))
    result_label.pack(pady=10)
    
    # 选项列表显示
    list_frame = ttk.LabelFrame(main_frame, text="所有可用选项预览", padding=10)
    list_frame.pack(fill="both", expand=True, pady=10)
    
    # 创建文本框显示所有选项
    text_frame = tk.Frame(list_frame)
    text_frame.pack(fill="both", expand=True)
    
    text_widget = tk.Text(text_frame, height=8, font=("微软雅黑", 9))
    scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
    
    text_widget.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    text_widget.config(yscrollcommand=scrollbar.set)
    
    # 填充所有选项到文本框
    for i, combo in enumerate(combinations, 1):
        text_widget.insert(tk.END, f"{i:2d}. {combo}\n")
    
    text_widget.config(state="disabled")  # 设为只读
    
    # 初始化显示
    on_selection_change()
    
    print(f"\n界面已启动，请：")
    print(f"1. 点击下拉框查看是否显示所有49个选项")
    print(f"2. 点击'验证选项数量'按钮确认")
    print(f"3. 在下方文本框中可以看到所有选项的完整列表")
    
    root.mainloop()

if __name__ == "__main__":
    test_dropdown_display()
